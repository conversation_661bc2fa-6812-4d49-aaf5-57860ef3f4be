<?php
/**
 * اختبار متغيرات الجلسة
 * Test Session Variables
 */

session_start();

echo "<h1>اختبار متغيرات الجلسة</h1>";

// عرض جميع متغيرات الجلسة
echo "<h2>متغيرات الجلسة الحالية:</h2>";

if (empty($_SESSION)) {
    echo "<div style='color: orange; padding: 10px; background: #fff3cd; border-radius: 5px;'>";
    echo "لا توجد جلسة نشطة. <a href='simple_login.php'>تسجيل الدخول</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 8px; text-align: right;'>المتغير</th>";
    echo "<th style='padding: 8px; text-align: right;'>القيمة</th>";
    echo "</tr>";
    
    foreach ($_SESSION as $key => $value) {
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>" . htmlspecialchars($key) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // اختبار الدوال
    echo "<h2>اختبار الدوال:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    // تضمين ملف الإعدادات لاختبار الدوال
    require_once 'config/config.php';
    
    echo "<strong>is_logged_in():</strong> " . (is_logged_in() ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>') . "<br>";
    echo "<strong>is_admin():</strong> " . (is_admin() ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>') . "<br>";
    echo "<strong>is_student():</strong> " . (is_student() ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>') . "<br>";
    
    echo "</div>";
    
    // اختبار الوصول للمتغيرات المختلفة
    echo "<h2>اختبار الوصول للمتغيرات:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    $name_tests = [
        '$_SESSION[\'user_name\']' => $_SESSION['user_name'] ?? 'غير موجود',
        '$_SESSION[\'full_name\']' => $_SESSION['full_name'] ?? 'غير موجود',
        'التوافق' => $_SESSION['user_name'] ?? $_SESSION['full_name'] ?? 'غير موجود'
    ];
    
    foreach ($name_tests as $test => $result) {
        $color = $result === 'غير موجود' ? 'red' : 'green';
        echo "<strong>{$test}:</strong> <span style='color: {$color};'>{$result}</span><br>";
    }
    
    echo "</div>";
    
    // روابط الاختبار
    echo "<h2>اختبار الصفحات:</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    if (is_admin()) {
        echo "<a href='admin/index.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة الإدارة</a>";
    }
    
    if (is_student()) {
        echo "<a href='student/dashboard.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة الطالب</a>";
    }
    
    echo "<a href='?logout=1' style='background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>تسجيل خروج</a>";
    echo "</div>";
}

// تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: test_session.php");
    exit();
}

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='simple_login.php'>تسجيل دخول بسيط</a></li>";
echo "<li><a href='quick_login.php'>تسجيل دخول سريع</a></li>";
echo "<li><a href='auth/login.php'>تسجيل دخول رسمي</a></li>";
echo "<li><a href='debug_login.php'>تشخيص المشاكل</a></li>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "</ul>";

// معلومات إضافية
echo "<hr>";
echo "<h3>معلومات الجلسة:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>معرف الجلسة:</strong> " . session_id() . "<br>";
echo "<strong>حالة الجلسة:</strong> " . session_status() . "<br>";
echo "<strong>اسم الجلسة:</strong> " . session_name() . "<br>";
echo "<strong>مسار الجلسة:</strong> " . session_save_path() . "<br>";
echo "</div>";
?>
