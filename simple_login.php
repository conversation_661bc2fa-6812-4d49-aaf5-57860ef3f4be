<?php
/**
 * تسجيل دخول بسيط جداً للاختبار
 * Very Simple Login for Testing
 */

// بدء الجلسة
session_start();

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'elzam_training_system';
$username = 'root';
$password = '';

$message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $pass = $_POST['password'] ?? '';
    
    if (empty($email) || empty($pass)) {
        $message = '<div style="color: red;">يرجى إدخال البريد الإلكتروني وكلمة المرور</div>';
    } else {
        try {
            // الاتصال بقاعدة البيانات
            $pdo = new PDO("mysql:host={$host};dbname={$dbname};charset=utf8mb4", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // البحث عن المستخدم
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1 LIMIT 1");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && password_verify($pass, $user['password'])) {
                if ($user['account_status'] === 'approved') {
                    // تسجيل الدخول بنجاح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_type'] = $user['user_type'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['full_name'] = $user['full_name']; // للتوافق مع الكود القديم
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['email'] = $user['email']; // للتوافق مع الكود القديم
                    
                    $message = '<div style="color: green; font-weight: bold;">✓ تم تسجيل الدخول بنجاح!</div>';
                    
                    // إعادة التوجيه
                    if ($user['user_type'] === 'admin') {
                        $redirect_url = 'admin/index.php';
                    } else {
                        $redirect_url = 'student/dashboard.php';
                    }
                    
                    $message .= '<div style="margin: 10px 0;">سيتم توجيهك خلال 3 ثواني...</div>';
                    $message .= '<script>setTimeout(function(){ window.location.href = "' . $redirect_url . '"; }, 3000);</script>';
                    $message .= '<a href="' . $redirect_url . '" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">اذهب الآن</a>';
                    
                } else {
                    $message = '<div style="color: orange;">حسابك في انتظار الموافقة من الإدارة</div>';
                }
            } else {
                $message = '<div style="color: red;">البريد الإلكتروني أو كلمة المرور غير صحيحة</div>';
            }
            
        } catch (Exception $e) {
            $message = '<div style="color: red;">خطأ: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
}

// التحقق من الجلسة الحالية
$current_user = '';
if (isset($_SESSION['user_id'])) {
    $current_user = '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;">
        <strong>مسجل دخول حالياً:</strong><br>
        الاسم: ' . htmlspecialchars($_SESSION['user_name'] ?? 'غير محدد') . '<br>
        البريد: ' . htmlspecialchars($_SESSION['user_email'] ?? 'غير محدد') . '<br>
        النوع: ' . ($_SESSION['user_type'] ?? 'غير محدد') . '<br>
        <a href="?logout=1" style="color: #721c24;">تسجيل خروج</a>
    </div>';
}

// تسجيل الخروج
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: simple_login.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول بسيط - الزام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 500px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .test-accounts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        
        .quick-fill {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            margin: 5px;
            border-radius: 3px;
            text-decoration: none;
            font-size: 12px;
        }
        
        .quick-fill:hover {
            background: #1e7e34;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333;">تسجيل دخول بسيط</h1>
        <p style="text-align: center; color: #666;">للاختبار والتطوير</p>
        
        <?php echo $current_user; ?>
        
        <?php if ($message): ?>
            <div style="margin: 15px 0;">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">البريد الإلكتروني:</label>
                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? '<EMAIL>'); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <button type="submit" id="loginBtn">تسجيل الدخول</button>
            </div>
        </form>
        
        <div class="test-accounts">
            <strong>حسابات تجريبية:</strong><br>
            <a href="#" class="quick-fill" onclick="fillAdmin()">مدير: <EMAIL> / admin123</a><br>
            <a href="#" class="quick-fill" onclick="fillStudent()">طالب: <EMAIL> / student123</a>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="auth/login.php" style="color: #007bff;">الصفحة الرسمية لتسجيل الدخول</a> |
            <a href="index.php" style="color: #007bff;">الصفحة الرئيسية</a> |
            <a href="debug_login.php" style="color: #007bff;">تشخيص المشاكل</a>
        </div>
    </div>
    
    <script>
        function fillAdmin() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'admin123';
        }
        
        function fillStudent() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'student123';
        }
        
        // معالجة النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.innerHTML = 'جاري تسجيل الدخول...';
            loginBtn.disabled = true;
            
            // إعادة تفعيل الزر بعد 10 ثواني
            setTimeout(function() {
                if (loginBtn.disabled) {
                    loginBtn.innerHTML = 'تسجيل الدخول';
                    loginBtn.disabled = false;
                }
            }, 10000);
        });
    </script>
</body>
</html>
