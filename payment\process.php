<?php
/**
 * معالج الدفع الإلكتروني
 * Payment Processor
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'الدفع الإلكتروني';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];
$course_id = (int)($_GET['course_id'] ?? 0);

if (!$course_id) {
    show_message('معرف الدورة غير صحيح', 'error');
    redirect(SITE_URL . '/student/courses.php');
}

// الحصول على بيانات الدورة
$course = $courseManager->getCourseById($course_id);
if (!$course) {
    show_message('الدورة غير موجودة', 'error');
    redirect(SITE_URL . '/student/courses.php');
}

// التحقق من التسجيل في الدورة
$enrollment = $database->fetchOne(
    "SELECT * FROM enrollments WHERE user_id = ? AND course_id = ?",
    [$user_id, $course_id]
);

if (!$enrollment) {
    show_message('لم تسجل في هذه الدورة', 'error');
    redirect(SITE_URL . '/student/courses.php');
}

if ($enrollment['payment_status'] === 'completed') {
    show_message('تم الدفع مسبقاً لهذه الدورة', 'info');
    redirect(SITE_URL . '/student/courses.php');
}

$message = '';
$message_type = '';

// معالجة الدفع
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'رمز الأمان غير صحيح';
        $message_type = 'error';
    } else {
        $payment_method = $_POST['payment_method'] ?? '';
        
        if ($payment_method === 'paypal') {
            // إنشاء دفعة جديدة
            $payment_id = $paymentManager->createPayment($user_id, $course_id, $enrollment['id'], $course['price']);
            
            if ($payment_id) {
                // محاكاة عملية الدفع (في البيئة الحقيقية ستكون مع PayPal API)
                $transaction_id = 'TXN_' . time() . '_' . $payment_id;
                
                // تحديث حالة الدفع
                $payment_result = $paymentManager->updatePaymentStatus(
                    $payment_id, 
                    'completed', 
                    $transaction_id,
                    json_encode(['status' => 'success', 'method' => 'paypal_sandbox'])
                );
                
                if ($payment_result) {
                    // إرسال إشعار للطالب
                    $userManager->sendNotification(
                        $user_id,
                        'تم الدفع بنجاح',
                        "تم دفع رسوم دورة '{$course['course_name']}' بنجاح. سيتم تفعيل الدورة قريباً.",
                        'payment'
                    );
                    
                    $message = 'تم الدفع بنجاح! سيتم تفعيل الدورة قريباً.';
                    $message_type = 'success';
                    
                    // إعادة التوجيه بعد 3 ثوان
                    header("refresh:3;url=" . SITE_URL . "/student/courses.php");
                } else {
                    $message = 'فشل في تحديث حالة الدفع';
                    $message_type = 'error';
                }
            } else {
                $message = 'فشل في إنشاء عملية الدفع';
                $message_type = 'error';
            }
        } else {
            $message = 'طريقة الدفع غير مدعومة';
            $message_type = 'error';
        }
    }
}

include '../includes/header.php';
?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white text-center py-4">
                    <i class="fas fa-credit-card fa-3x mb-3"></i>
                    <h3 class="mb-0">الدفع الإلكتروني</h3>
                    <p class="mb-0">إكمال دفع رسوم الدورة</p>
                </div>
                
                <div class="card-body p-5">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Course Details -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <?php if ($course['course_image']): ?>
                                <img src="../assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                                     class="img-fluid rounded" alt="<?php echo htmlspecialchars($course['course_name']); ?>">
                            <?php else: ?>
                                <div class="bg-primary rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-book fa-3x text-white"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-8">
                            <h4 class="fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h4>
                            <p class="text-muted mb-2">
                                <i class="fas fa-user me-2"></i>
                                المدرب: <?php echo htmlspecialchars($course['instructor_name']); ?>
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-clock me-2"></i>
                                المدة: <?php echo $course['duration_hours']; ?> ساعة
                            </p>
                            <p class="text-muted mb-3">
                                <i class="fas fa-calendar me-2"></i>
                                تاريخ البدء: <?php echo $course['start_date'] ? format_date($course['start_date'], 'Y/m/d') : 'سيتم تحديده لاحقاً'; ?>
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h3 text-primary mb-0">
                                    <?php echo format_currency($course['price']); ?>
                                </span>
                                <span class="badge bg-info">
                                    <?php echo $course['current_enrolled']; ?>/<?php echo $course['max_students']; ?> طالب
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Payment Form -->
                    <form method="POST" id="paymentForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        
                        <h5 class="mb-3">
                            <i class="fas fa-credit-card me-2"></i>اختر طريقة الدفع
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card payment-method" data-method="paypal">
                                    <div class="card-body text-center">
                                        <i class="fab fa-paypal fa-3x text-primary mb-3"></i>
                                        <h6 class="fw-bold">PayPal</h6>
                                        <p class="text-muted small mb-0">دفع آمن عبر PayPal</p>
                                        <input type="radio" name="payment_method" value="paypal" class="d-none" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <div class="card payment-method" data-method="stripe">
                                    <div class="card-body text-center">
                                        <i class="fab fa-stripe fa-3x text-info mb-3"></i>
                                        <h6 class="fw-bold">Stripe</h6>
                                        <p class="text-muted small mb-0">دفع بالبطاقة الائتمانية</p>
                                        <input type="radio" name="payment_method" value="stripe" class="d-none" disabled>
                                        <small class="text-muted d-block mt-2">قريباً</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Summary -->
                        <div class="card bg-light mt-4">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">ملخص الدفع</h6>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>رسوم الدورة:</span>
                                    <span><?php echo format_currency($course['price']); ?></span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>رسوم المعالجة:</span>
                                    <span class="text-success">مجاناً</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold h5">
                                    <span>المجموع:</span>
                                    <span class="text-primary"><?php echo format_currency($course['price']); ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="<?php echo SITE_URL; ?>/terms.php" target="_blank">شروط الاستخدام</a> 
                                و <a href="<?php echo SITE_URL; ?>/refund-policy.php" target="_blank">سياسة الاسترداد</a>
                            </label>
                        </div>
                        
                        <!-- Payment Button -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="payButton" disabled>
                                <i class="fas fa-lock me-2"></i>دفع آمن - <?php echo format_currency($course['price']); ?>
                            </button>
                            <a href="../student/courses.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>العودة إلى دوراتي
                            </a>
                        </div>
                    </form>
                    
                    <!-- Security Notice -->
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>أمان الدفع:</strong> جميع المعاملات محمية بتشفير SSL وتتم معالجتها عبر بوابات دفع آمنة.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.payment-method {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.payment-method:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}

.payment-method.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.payment-method.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.payment-method.disabled:hover {
    transform: none;
    box-shadow: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const payButton = document.getElementById('payButton');
    const termsCheckbox = document.getElementById('terms');
    
    // اختيار طريقة الدفع
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            if (this.classList.contains('disabled')) return;
            
            // إزالة التحديد من جميع الطرق
            paymentMethods.forEach(m => m.classList.remove('selected'));
            
            // تحديد الطريقة المختارة
            this.classList.add('selected');
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
            
            updatePayButton();
        });
    });
    
    // التحقق من الشروط والأحكام
    termsCheckbox.addEventListener('change', updatePayButton);
    
    function updatePayButton() {
        const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
        const termsAccepted = termsCheckbox.checked;
        
        payButton.disabled = !(selectedMethod && termsAccepted);
    }
    
    // تحسين تجربة المستخدم عند الإرسال
    document.getElementById('paymentForm').addEventListener('submit', function(e) {
        const submitBtn = payButton;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
        submitBtn.disabled = true;
    });
});

// محاكاة PayPal (في البيئة الحقيقية ستستخدم PayPal SDK)
function processPayPalPayment() {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                success: true,
                transactionId: 'TXN_' + Date.now(),
                amount: <?php echo $course['price']; ?>
            });
        }, 2000);
    });
}
</script>

<?php include '../includes/footer.php'; ?>
