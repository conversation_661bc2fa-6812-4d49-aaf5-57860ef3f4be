<?php
/**
 * إدارة الدورات
 * Courses Management
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!is_logged_in() || !is_admin()) {
    redirect(SITE_URL . '/auth/login.php');
}

$page_title = 'إدارة الدورات';

// معالجة العمليات
$message = '';
$message_type = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_course':
            try {
                // التحقق من البيانات المطلوبة
                $course_name = trim($_POST['course_name'] ?? '');
                $instructor_name = trim($_POST['instructor_name'] ?? '');
                $duration_hours = (int)($_POST['duration_hours'] ?? 0);
                $price = (float)($_POST['price'] ?? 0);

                if (empty($course_name) || empty($instructor_name) || $duration_hours <= 0 || $price < 0) {
                    $message = 'يرجى ملء جميع البيانات المطلوبة بشكل صحيح';
                    $message_type = 'error';
                } else {
                    $course_data = [
                        'course_name' => $course_name,
                        'course_description' => trim($_POST['course_description'] ?? ''),
                        'instructor_name' => $instructor_name,
                        'duration_hours' => $duration_hours,
                        'price' => $price,
                        'start_date' => $_POST['start_date'] ?: null,
                        'end_date' => $_POST['end_date'] ?: null,
                        'max_students' => (int)($_POST['max_students'] ?? 50),
                        'course_link' => trim($_POST['course_link'] ?? ''),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    $course_id = $database->insert('courses', $course_data);

                    if ($course_id) {
                        $message = 'تم إضافة الدورة بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في إضافة الدورة';
                        $message_type = 'error';
                    }
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ أثناء إضافة الدورة: ' . $e->getMessage();
                $message_type = 'error';
            }
            break;
                
        case 'update_course':
            try {
                $course_id = (int)($_POST['course_id'] ?? 0);
                if ($course_id <= 0) {
                    $message = 'معرف الدورة غير صحيح';
                    $message_type = 'error';
                } else {
                    $course_data = [
                        'course_name' => trim($_POST['course_name'] ?? ''),
                        'course_description' => trim($_POST['course_description'] ?? ''),
                        'instructor_name' => trim($_POST['instructor_name'] ?? ''),
                        'duration_hours' => (int)($_POST['duration_hours'] ?? 0),
                        'price' => (float)($_POST['price'] ?? 0),
                        'start_date' => $_POST['start_date'] ?: null,
                        'end_date' => $_POST['end_date'] ?: null,
                        'max_students' => (int)($_POST['max_students'] ?? 50),
                        'course_link' => trim($_POST['course_link'] ?? ''),
                        'is_active' => isset($_POST['is_active']) ? 1 : 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $result = $database->update('courses', $course_data, 'id = ?', [$course_id]);

                    if ($result) {
                        $message = 'تم تحديث الدورة بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في تحديث الدورة';
                        $message_type = 'error';
                    }
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ أثناء تحديث الدورة: ' . $e->getMessage();
                $message_type = 'error';
            }
            break;

        case 'toggle_status':
            try {
                $course_id = (int)($_POST['course_id'] ?? 0);
                $current_status = (int)($_POST['current_status'] ?? 0);
                $new_status = $current_status ? 0 : 1;

                if ($course_id <= 0) {
                    $message = 'معرف الدورة غير صحيح';
                    $message_type = 'error';
                } else {
                    $result = $database->update('courses', ['is_active' => $new_status], 'id = ?', [$course_id]);

                    if ($result) {
                        $message = $new_status ? 'تم تفعيل الدورة' : 'تم إلغاء تفعيل الدورة';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في تغيير حالة الدورة';
                        $message_type = 'error';
                    }
                }
            } catch (Exception $e) {
                $message = 'حدث خطأ أثناء تغيير حالة الدورة: ' . $e->getMessage();
                $message_type = 'error';
            }
            break;
    }
}

// الحصول على جميع الدورات
$courses = [];
if ($db) {
    try {
        $courses = $database->fetchAll("
            SELECT c.*, 
                   COUNT(e.id) as enrolled_count,
                   SUM(CASE WHEN e.payment_status = 'completed' THEN 1 ELSE 0 END) as paid_count
            FROM courses c 
            LEFT JOIN enrollments e ON c.id = e.course_id 
            GROUP BY c.id 
            ORDER BY c.created_at DESC
        ");
    } catch (Exception $e) {
        $courses = $database->fetchAll("SELECT * FROM courses ORDER BY created_at DESC");
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column">
                <h5 class="text-white mb-4 text-center">
                    <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                    <a class="nav-link active" href="courses.php">
                        <i class="fas fa-book me-2"></i>إدارة الدورات
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>إدارة الطلاب
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-user-graduate me-2"></i>التسجيلات
                    </a>
                    <a class="nav-link" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>المدفوعات
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book me-2"></i>إدارة الدورات</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                    <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
                </button>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-book fa-2x text-primary mb-2"></i>
                            <h5><?php echo count($courses); ?></h5>
                            <small class="text-muted">إجمالي الدورات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5><?php echo count(array_filter($courses, function($c) { return $c['is_active']; })); ?></h5>
                            <small class="text-muted">دورات نشطة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                            <h5><?php echo array_sum(array_column($courses, 'enrolled_count')); ?></h5>
                            <small class="text-muted">إجمالي المسجلين</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-dollar-sign fa-2x text-warning mb-2"></i>
                            <h5><?php echo number_format(array_sum(array_map(function($c) { return $c['price'] * ($c['paid_count'] ?? 0); }, $courses)), 2); ?></h5>
                            <small class="text-muted">إجمالي الإيرادات</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- جدول الدورات -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم الدورة</th>
                                    <th>المدرب</th>
                                    <th>المدة</th>
                                    <th>السعر</th>
                                    <th>المسجلين</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($courses)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i><br>
                                            لا توجد دورات مضافة بعد
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($courses as $course): ?>
                                        <tr>
                                            <td><?php echo $course['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($course['course_name']); ?></strong>
                                                <?php if ($course['start_date']): ?>
                                                    <br><small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        <?php echo date('Y/m/d', strtotime($course['start_date'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($course['instructor_name']); ?></td>
                                            <td><?php echo $course['duration_hours']; ?> ساعة</td>
                                            <td><?php echo number_format($course['price'], 2); ?> USD</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo $course['enrolled_count'] ?? 0; ?>/<?php echo $course['max_students']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $course['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $course['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm" 
                                                            onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                        <input type="hidden" name="current_status" value="<?php echo $course['is_active']; ?>">
                                                        <button type="submit" class="btn btn-outline-<?php echo $course['is_active'] ? 'warning' : 'success'; ?> btn-sm">
                                                            <i class="fas fa-<?php echo $course['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة دورة -->
<div class="modal fade" id="addCourseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة دورة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_course">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الدورة</label>
                            <input type="text" class="form-control" name="course_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم المدرب</label>
                            <input type="text" class="form-control" name="instructor_name" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">وصف الدورة</label>
                        <textarea class="form-control" name="course_description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">المدة (ساعة)</label>
                            <input type="number" class="form-control" name="duration_hours" min="1" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">السعر (USD)</label>
                            <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">الحد الأقصى للطلاب</label>
                            <input type="number" class="form-control" name="max_students" min="1" value="50" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ البداية</label>
                            <input type="date" class="form-control" name="start_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ النهاية</label>
                            <input type="date" class="form-control" name="end_date">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">رابط الدورة</label>
                        <input type="url" class="form-control" name="course_link" placeholder="https://...">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            دورة نشطة
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الدورة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function editCourse(course) {
    // يمكن إضافة modal للتعديل هنا
    alert('ميزة التعديل ستتم إضافتها قريباً');
}

// معالجة نموذج إضافة الدورة
document.addEventListener('DOMContentLoaded', function() {
    const addCourseForm = document.querySelector('#addCourseModal form');
    if (addCourseForm) {
        addCourseForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // تعطيل الزر وتغيير النص
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
            submitBtn.disabled = true;

            // السماح للنموذج بالإرسال العادي
            // سيتم إعادة تحميل الصفحة وعرض النتيجة
        });
    }

    // إغلاق المودال بعد الإرسال الناجح
    <?php if ($message && $message_type === 'success'): ?>
    const modal = bootstrap.Modal.getInstance(document.getElementById('addCourseModal'));
    if (modal) {
        modal.hide();
    }

    // عرض رسالة نجاح
    if (typeof showSuccess === 'function') {
        showSuccess('<?php echo addslashes($message); ?>');
    }
    <?php endif; ?>

    <?php if ($message && $message_type === 'error'): ?>
    // عرض رسالة خطأ
    if (typeof showError === 'function') {
        showError('<?php echo addslashes($message); ?>');
    }
    <?php endif; ?>
});
</script>

<?php include '../includes/footer.php'; ?>
