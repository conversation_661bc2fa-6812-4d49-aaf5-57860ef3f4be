<?php
/**
 * معالج التثبيت التلقائي
 * Automatic Installation Handler
 */

// التحقق من وجود ملف الإعدادات
if (file_exists('config/database.php')) {
    $config_exists = true;
    require_once 'config/database.php';
} else {
    $config_exists = false;
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $step = 2;
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_name = $_POST['db_name'] ?? 'elzam_training_system';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';
            
            try {
                // اختبار الاتصال
                $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$db_name}`");
                
                // تنفيذ سكريبت قاعدة البيانات
                $sql = file_get_contents('database/schema.sql');
                $pdo->exec($sql);
                
                // إنشاء ملف الإعدادات
                $config_content = "<?php
/**
 * إعدادات قاعدة البيانات - تم إنشاؤها تلقائياً
 */

define('DB_HOST', '{$db_host}');
define('DB_NAME', '{$db_name}');
define('DB_USER', '{$db_user}');
define('DB_PASS', '{$db_pass}');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_LIFETIME', 3600);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 900);

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$charset = DB_CHARSET;
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4\"
            ];
            
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
            
        } catch(PDOException \$exception) {
            error_log(\"Connection error: \" . \$exception->getMessage());
            die(\"خطأ في الاتصال بقاعدة البيانات\");
        }
        
        return \$this->conn;
    }

    public function closeConnection() {
        \$this->conn = null;
    }

    public function executeQuery(\$query, \$params = []) {
        try {
            \$stmt = \$this->conn->prepare(\$query);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            error_log(\"Query error: \" . \$e->getMessage());
            return false;
        }
    }

    public function fetchOne(\$query, \$params = []) {
        \$stmt = \$this->executeQuery(\$query, \$params);
        return \$stmt ? \$stmt->fetch() : false;
    }

    public function fetchAll(\$query, \$params = []) {
        \$stmt = \$this->executeQuery(\$query, \$params);
        return \$stmt ? \$stmt->fetchAll() : false;
    }

    public function insert(\$table, \$data) {
        \$columns = implode(',', array_keys(\$data));
        \$placeholders = ':' . implode(', :', array_keys(\$data));
        
        \$query = \"INSERT INTO {\$table} ({\$columns}) VALUES ({\$placeholders})\";
        \$stmt = \$this->executeQuery(\$query, \$data);
        
        return \$stmt ? \$this->conn->lastInsertId() : false;
    }

    public function update(\$table, \$data, \$where, \$whereParams = []) {
        \$setClause = [];
        foreach(\$data as \$key => \$value) {
            \$setClause[] = \"{\$key} = :{\$key}\";
        }
        \$setClause = implode(', ', \$setClause);
        
        \$query = \"UPDATE {\$table} SET {\$setClause} WHERE {\$where}\";
        \$params = array_merge(\$data, \$whereParams);
        
        return \$this->executeQuery(\$query, \$params);
    }

    public function delete(\$table, \$where, \$params = []) {
        \$query = \"DELETE FROM {\$table} WHERE {\$where}\";
        return \$this->executeQuery(\$query, \$params);
    }
}

\$database = new Database();
\$db = \$database->getConnection();

if (!\$db) {
    die(\"فشل في الاتصال بقاعدة البيانات\");
}
?>";
                
                file_put_contents('config/database.php', $config_content);
                
                $step = 3;
                $success = 'تم إعداد قاعدة البيانات بنجاح!';
                
            } catch (Exception $e) {
                $error = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 3:
            // إعداد المشرف
            $admin_name = $_POST['admin_name'] ?? '';
            $admin_email = $_POST['admin_email'] ?? '';
            $admin_password = $_POST['admin_password'] ?? '';
            
            if (empty($admin_name) || empty($admin_email) || empty($admin_password)) {
                $error = 'جميع الحقول مطلوبة';
            } else {
                try {
                    require_once 'config/database.php';
                    
                    // تحديث بيانات المشرف
                    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                    $stmt = $db->prepare("UPDATE users SET full_name = ?, email = ?, password = ? WHERE user_type = 'admin'");
                    $stmt->execute([$admin_name, $admin_email, $hashed_password]);
                    
                    $step = 4;
                    $success = 'تم إعداد حساب المشرف بنجاح!';
                    
                } catch (Exception $e) {
                    $error = 'خطأ في إعداد المشرف: ' . $e->getMessage();
                }
            }
            break;
    }
}

// التحقق من المتطلبات
function checkRequirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'MySQL Extension' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'FileInfo Extension' => extension_loaded('fileinfo'),
        'Session Support' => function_exists('session_start'),
        'JSON Support' => function_exists('json_encode'),
        'Config Directory Writable' => is_writable('config') || is_writable('.'),
        'Assets Directory Writable' => is_writable('assets') || is_writable('.')
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$all_requirements_met = !in_array(false, $requirements);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة الدورات التدريبية - الزام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .install-container {
            max-width: 800px;
            margin: 50px auto;
        }
        
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            position: relative;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .requirement:last-child {
            border-bottom: none;
        }
        
        .requirement-status {
            font-size: 1.2em;
        }
        
        .requirement-pass {
            color: #28a745;
        }
        
        .requirement-fail {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-card">
                <div class="install-header">
                    <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                    <h2>نظام إدارة الدورات التدريبية - الزام</h2>
                    <p class="mb-0">معالج التثبيت التلقائي</p>
                </div>
                
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                    <div class="step <?php echo $step >= 4 ? 'active' : ''; ?>">4</div>
                </div>
                
                <div class="p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step == 1): ?>
                        <!-- Step 1: Requirements Check -->
                        <h4 class="mb-4">الخطوة 1: التحقق من المتطلبات</h4>
                        
                        <div class="requirements-list">
                            <?php foreach ($requirements as $requirement => $status): ?>
                                <div class="requirement">
                                    <span><?php echo $requirement; ?></span>
                                    <span class="requirement-status <?php echo $status ? 'requirement-pass' : 'requirement-fail'; ?>">
                                        <i class="fas fa-<?php echo $status ? 'check-circle' : 'times-circle'; ?>"></i>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if ($all_requirements_met): ?>
                            <form method="POST" class="mt-4">
                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-arrow-left me-2"></i>المتابعة إلى الخطوة التالية
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning mt-4">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                يرجى حل المشاكل أعلاه قبل المتابعة
                            </div>
                        <?php endif; ?>
                        
                    <?php elseif ($step == 2): ?>
                        <!-- Step 2: Database Setup -->
                        <h4 class="mb-4">الخطوة 2: إعداد قاعدة البيانات</h4>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="elzam_training_system" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_user" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_pass" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-database me-2"></i>إعداد قاعدة البيانات
                            </button>
                        </form>
                        
                    <?php elseif ($step == 3): ?>
                        <!-- Step 3: Admin Setup -->
                        <h4 class="mb-4">الخطوة 3: إعداد حساب المشرف</h4>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="admin_name" class="form-label">اسم المشرف</label>
                                <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="admin_password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="admin_password" name="admin_password" minlength="8" required>
                                <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-user-shield me-2"></i>إنشاء حساب المشرف
                            </button>
                        </form>
                        
                    <?php elseif ($step == 4): ?>
                        <!-- Step 4: Installation Complete -->
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                            <h3 class="text-success mb-4">تم التثبيت بنجاح!</h3>
                            <p class="lead mb-4">
                                تم تثبيت نظام إدارة الدورات التدريبية بنجاح. يمكنك الآن البدء في استخدام النظام.
                            </p>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <a href="index.php" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="auth/login.php" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </a>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>مهم:</strong> احذف ملف install.php من الخادم لأسباب أمنية.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
