<?php
/**
 * إنشاء الجداول المطلوبة
 * Create Required Tables
 */

require_once 'config/config.php';

echo "<h1>إنشاء الجداول المطلوبة</h1>";

if (!$db) {
    echo "<div style='color: red; font-weight: bold;'>خطأ: لا يوجد اتصال بقاعدة البيانات</div>";
    exit;
}

$message = '';
$message_type = '';

// إنشاء الجداول
if (isset($_POST['create_tables'])) {
    try {
        // جدول المستخدمين
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                student_id VARCHAR(50) UNIQUE,
                full_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                user_type ENUM('admin', 'student') DEFAULT 'student',
                university_level VARCHAR(100),
                specialization VARCHAR(255),
                university_card_image VARCHAR(255),
                account_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                is_active TINYINT(1) DEFAULT 1,
                remember_token VARCHAR(255),
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول المستخدمين</span><br>";

        // جدول الدورات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_name VARCHAR(255) NOT NULL,
                course_description TEXT,
                instructor_name VARCHAR(255) NOT NULL,
                duration_hours INT NOT NULL,
                price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                start_date DATE NULL,
                end_date DATE NULL,
                max_students INT DEFAULT 50,
                course_link VARCHAR(500),
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول الدورات</span><br>";

        // جدول التسجيلات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                enrollment_status ENUM('pending', 'active', 'completed', 'cancelled') DEFAULT 'pending',
                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                completion_date TIMESTAMP NULL,
                progress_percentage DECIMAL(5,2) DEFAULT 0.00,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                UNIQUE KEY unique_enrollment (user_id, course_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول التسجيلات</span><br>";

        // جدول المدفوعات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                enrollment_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_method ENUM('paypal', 'stripe', 'bank_transfer', 'cash') DEFAULT 'paypal',
                payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                transaction_id VARCHAR(255),
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول المدفوعات</span><br>";

        // جدول الشهادات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS certificates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                enrollment_id INT NOT NULL,
                certificate_number VARCHAR(100) UNIQUE NOT NULL,
                issued_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                certificate_file VARCHAR(255),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول الشهادات</span><br>";

        // جدول الإشعارات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                notification_type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول الإشعارات</span><br>";

        // جدول سجل النشاطات
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                activity_type VARCHAR(100),
                activity_description TEXT,
                page_accessed VARCHAR(255),
                session_id VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<span style='color: green;'>✓ تم إنشاء جدول سجل النشاطات</span><br>";

        $message = 'تم إنشاء جميع الجداول بنجاح';
        $message_type = 'success';

    } catch (Exception $e) {
        $message = 'خطأ في إنشاء الجداول: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// إنشاء بيانات تجريبية
if (isset($_POST['create_sample_data'])) {
    try {
        // إنشاء مدير
        $admin_exists = $database->fetchOne("SELECT id FROM users WHERE email = '<EMAIL>'");
        if (!$admin_exists) {
            $admin_data = [
                'student_id' => 'ADMIN001',
                'full_name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'user_type' => 'admin',
                'account_status' => 'approved',
                'is_active' => 1
            ];
            $database->insert('users', $admin_data);
            echo "<span style='color: green;'>✓ تم إنشاء حساب المدير</span><br>";
        }

        // إنشاء طالب تجريبي
        $student_exists = $database->fetchOne("SELECT id FROM users WHERE email = '<EMAIL>'");
        if (!$student_exists) {
            $student_data = [
                'student_id' => 'STU001',
                'full_name' => 'طالب تجريبي',
                'email' => '<EMAIL>',
                'password' => password_hash('student123', PASSWORD_DEFAULT),
                'user_type' => 'student',
                'university_level' => 'السنة الثالثة',
                'specialization' => 'علوم الحاسوب',
                'account_status' => 'approved',
                'is_active' => 1
            ];
            $database->insert('users', $student_data);
            echo "<span style='color: green;'>✓ تم إنشاء حساب الطالب</span><br>";
        }

        // إنشاء دورات تجريبية
        $course_exists = $database->fetchOne("SELECT id FROM courses LIMIT 1");
        if (!$course_exists) {
            $courses = [
                [
                    'course_name' => 'أساسيات البرمجة',
                    'course_description' => 'دورة تعليمية شاملة في أساسيات البرمجة',
                    'instructor_name' => 'د. أحمد محمد',
                    'duration_hours' => 40,
                    'price' => 99.99,
                    'max_students' => 30,
                    'course_link' => 'https://www.youtube.com/watch?v=example1',
                    'is_active' => 1
                ],
                [
                    'course_name' => 'تطوير المواقع',
                    'course_description' => 'تعلم تطوير المواقع باستخدام HTML, CSS, JavaScript',
                    'instructor_name' => 'أ. فاطمة علي',
                    'duration_hours' => 60,
                    'price' => 149.99,
                    'max_students' => 25,
                    'course_link' => 'https://www.youtube.com/watch?v=example2',
                    'is_active' => 1
                ]
            ];

            foreach ($courses as $course) {
                $database->insert('courses', $course);
            }
            echo "<span style='color: green;'>✓ تم إنشاء دورات تجريبية</span><br>";
        }

        $message = 'تم إنشاء البيانات التجريبية بنجاح';
        $message_type = 'success';

    } catch (Exception $e) {
        $message = 'خطأ في إنشاء البيانات التجريبية: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// عرض الجداول الموجودة
echo "<h2>الجداول الموجودة:</h2>";
try {
    $tables = $database->fetchAll("SHOW TABLES");
    if (is_array($tables) && count($tables) > 0) {
        echo "<ul>";
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            echo "<li style='color: green;'>✓ {$table_name}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>لا توجد جداول</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب الجداول: " . $e->getMessage() . "</p>";
}

if ($message) {
    $color = $message_type === 'error' ? 'red' : 'green';
    echo "<div style='color: {$color}; padding: 10px; background: #f8f9fa; border-radius: 5px; margin: 20px 0;'>";
    echo $message;
    echo "</div>";
}
?>

<form method="POST" style="margin: 20px 0;">
    <button type="submit" name="create_tables" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
        إنشاء الجداول
    </button>
    <button type="submit" name="create_sample_data" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        إنشاء بيانات تجريبية
    </button>
</form>

<div style="margin: 20px 0;">
    <h3>روابط الاختبار:</h3>
    <ul>
        <li><a href="auth/login.php">صفحة تسجيل الدخول</a></li>
        <li><a href="auth/register.php">صفحة التسجيل</a></li>
        <li><a href="admin/courses.php">إدارة الدورات</a></li>
        <li><a href="student/dashboard.php">لوحة الطالب</a></li>
    </ul>
</div>
