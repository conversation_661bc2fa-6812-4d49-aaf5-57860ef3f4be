<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

// بدء الجلسة أولاً
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين الملفات مع معالجة الأخطاء
try {
    require_once '../config/config.php';
} catch (Exception $e) {
    die('خطأ في تحميل إعدادات النظام: ' . $e->getMessage());
}

try {
    require_once '../includes/functions.php';
} catch (Exception $e) {
    // يمكن المتابعة بدون functions.php إذا كانت الدوال في config.php
}

$page_title = 'تسجيل الدخول';

// إعادة توجيه المستخدمين المسجلين
if (isset($_SESSION['user_id']) && isset($_SESSION['user_type'])) {
    if ($_SESSION['user_type'] === 'admin') {
        header("Location: " . SITE_URL . "/admin/index.php");
        exit();
    } else {
        header("Location: " . SITE_URL . "/student/dashboard.php");
        exit();
    }
}

$error_message = '';
$success_message = '';

// التحقق من انتهاء الجلسة
if (isset($_GET['timeout'])) {
    $error_message = 'انتهت صلاحية جلستك. يرجى تسجيل الدخول مرة أخرى.';
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);

    // التحقق من البيانات الأساسية
    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال جميع البيانات المطلوبة';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        // التحقق من وجود اتصال بقاعدة البيانات
        if (!isset($database) || !isset($db) || !$db) {
            $error_message = 'خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.';
        } else {
            try {
                // البحث عن المستخدم
                $user = $database->fetchOne(
                    "SELECT * FROM users WHERE email = ? AND is_active = 1 LIMIT 1",
                    [$email]
                );

                if ($user && password_verify($password, $user['password'])) {
                    // التحقق من حالة الحساب
                    if ($user['account_status'] !== 'approved') {
                        $error_message = 'حسابك في انتظار الموافقة من الإدارة';
                    } else {
                        // تسجيل الدخول بنجاح
                        session_regenerate_id(true);
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_type'] = $user['user_type'];
                        $_SESSION['user_name'] = $user['full_name'];
                        $_SESSION['full_name'] = $user['full_name'];
                        $_SESSION['user_email'] = $user['email'];
                        $_SESSION['email'] = $user['email'];
                        $_SESSION['last_activity'] = time();

                        // تحديث آخر تسجيل دخول
                        try {
                            $database->executeQuery("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
                        } catch (Exception $e) {
                            // تجاهل خطأ التحديث
                        }

                        // تذكرني
                        if ($remember_me) {
                            try {
                                $token = bin2hex(random_bytes(16));
                                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/');
                                $database->executeQuery("UPDATE users SET remember_token = ? WHERE id = ?", [password_hash($token, PASSWORD_DEFAULT), $user['id']]);
                            } catch (Exception $e) {
                                // تجاهل خطأ التذكر
                            }
                        }

                        // إعادة التوجيه
                        if ($user['user_type'] === 'admin') {
                            header("Location: " . SITE_URL . "/admin/index.php");
                            exit();
                        } else {
                            header("Location: " . SITE_URL . "/student/dashboard.php");
                            exit();
                        }
                    }
                } else {
                    $error_message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء تسجيل الدخول: ' . $e->getMessage();
                error_log("Login error: " . $e->getMessage());
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row min-vh-100">
        <!-- الجانب الأيسر - معلومات الموقع -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center" 
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="text-center text-white p-5">
                <i class="fas fa-graduation-cap fa-5x mb-4"></i>
                <h2 class="mb-4">مرحباً بك في منصة الزام التعليمية</h2>
                <p class="lead mb-4">
                    انضم إلى آلاف الطلاب واكتسب مهارات جديدة من خلال دوراتنا التدريبية المتميزة
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h5>+1000</h5>
                        <small>طالب مسجل</small>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-book fa-2x mb-2"></i>
                        <h5>+50</h5>
                        <small>دورة تدريبية</small>
                    </div>
                    <div class="col-4">
                        <i class="fas fa-certificate fa-2x mb-2"></i>
                        <h5>+800</h5>
                        <small>شهادة صادرة</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
            <div class="w-100" style="max-width: 400px;">
                <div class="card shadow-lg border-0">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-sign-in-alt fa-3x text-primary mb-3"></i>
                            <h3 class="card-title">تسجيل الدخول</h3>
                            <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                        </div>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="" id="loginForm">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                       required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    تذكرني لمدة 30 يوماً
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </form>
                        
                        <div class="text-center">
                            <a href="forgot-password.php" class="text-decoration-none">
                                نسيت كلمة المرور؟
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-0">ليس لديك حساب؟</p>
                            <a href="register.php" class="btn btn-outline-primary w-100 mt-2">
                                <i class="fas fa-user-plus me-2"></i>إنشاء حساب جديد
                            </a>
                        </div>
                        
                        <!-- بيانات تجريبية للاختبار -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <small class="text-muted">
                                <strong>بيانات تجريبية للاختبار:</strong><br>
                                المدير: <EMAIL> / admin123<br>
                                طالب: <EMAIL> / student123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إظهار/إخفاء كلمة المرور
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// تحسين تجربة المستخدم
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // تعطيل الزر وتغيير النص
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
    submitBtn.disabled = true;

    // إعادة تفعيل الزر بعد 10 ثواني كحد أقصى
    setTimeout(function() {
        if (submitBtn.disabled) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }, 10000);
});

// التركيز على حقل البريد الإلكتروني
document.getElementById('email').focus();
</script>

<?php include '../includes/footer.php'; ?>
