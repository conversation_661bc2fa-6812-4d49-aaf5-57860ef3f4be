<?php
/**
 * إصلاح صفحة إدارة الدورات
 * Fix Courses Management Page
 */

echo "<h1>إصلاح صفحة إدارة الدورات</h1>";

$message = '';
$message_type = '';

if (isset($_POST['fix_page'])) {
    try {
        // نسخ الملف المحسن فوق الأصلي
        if (copy('admin/courses_fixed.php', 'admin/courses.php')) {
            $message = 'تم إصلاح صفحة إدارة الدورات بنجاح!';
            $message_type = 'success';
        } else {
            $message = 'فشل في نسخ الملف';
            $message_type = 'error';
        }
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

if ($message) {
    $color = $message_type === 'error' ? 'red' : 'green';
    echo "<div style='color: {$color}; padding: 10px; background: #f8f9fa; border-radius: 5px; margin: 20px 0;'>";
    echo $message;
    echo "</div>";
}

echo "<h2>المشكلة:</h2>";
echo "<p>صفحة إدارة الدورات الأصلية لا تعمل بسبب مشاكل في الكود.</p>";

echo "<h2>الحل:</h2>";
echo "<p>تم إنشاء نسخة محسنة من الصفحة تحل جميع المشاكل:</p>";
echo "<ul>";
echo "<li>✅ إزالة تداخل الأقواس</li>";
echo "<li>✅ تحسين معالجة النماذج</li>";
echo "<li>✅ إضافة إعادة توجيه بعد النجاح</li>";
echo "<li>✅ تحسين JavaScript</li>";
echo "<li>✅ رسائل خطأ واضحة</li>";
echo "</ul>";

echo "<h2>الاختبار:</h2>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>الصفحة المحسنة (تعمل بشكل مثالي):</h3>";
echo "<a href='admin/courses_fixed.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار الصفحة المحسنة</a>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>الصفحة الأصلية (بها مشاكل):</h3>";
echo "<a href='admin/courses.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الصفحة الأصلية</a>";
echo "</div>";
?>

<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>استبدال الصفحة الأصلية بالنسخة المحسنة:</h3>
    <p style="color: #666;">هذا سيستبدل ملف admin/courses.php بالنسخة المحسنة</p>
    <button type="submit" name="fix_page" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        إصلاح الصفحة الآن
    </button>
</form>

<div style="margin: 20px 0;">
    <h3>خطوات الاختبار:</h3>
    <ol>
        <li>اختبر الصفحة المحسنة أولاً للتأكد من عملها</li>
        <li>إذا كانت تعمل بشكل مثالي، اضغط "إصلاح الصفحة الآن"</li>
        <li>بعد الإصلاح، اختبر الصفحة الأصلية مرة أخرى</li>
    </ol>
</div>

<div style="margin: 20px 0;">
    <h3>روابط مفيدة:</h3>
    <ul>
        <li><a href="test_add_course.php">اختبار إضافة دورة مباشر</a></li>
        <li><a href="debug_form.php">تشخيص النماذج</a></li>
        <li><a href="test_final.php">اختبار شامل للنظام</a></li>
    </ul>
</div>
