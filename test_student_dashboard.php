<?php
/**
 * اختبار لوحة الطالب
 * Test Student Dashboard
 */

session_start();

echo "<h1>اختبار لوحة الطالب</h1>";

// التحقق من الجلسة
if (!isset($_SESSION['user_id'])) {
    echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
    echo "يجب تسجيل الدخول أولاً. <a href='simple_login.php'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

// تضمين الملفات المطلوبة
require_once 'config/config.php';

echo "<h2>1. فحص الجلسة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>معرف المستخدم:</strong> " . $_SESSION['user_id'] . "<br>";
echo "<strong>نوع المستخدم:</strong> " . ($_SESSION['user_type'] ?? 'غير محدد') . "<br>";
echo "<strong>اسم المستخدم:</strong> " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>";
echo "</div>";

// فحص قاعدة البيانات
echo "<h2>2. فحص قاعدة البيانات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

try {
    if ($database) {
        echo "<span style='color: green;'>✓ كائن قاعدة البيانات متاح</span><br>";
        
        // اختبار جلب بيانات المستخدم
        $user = $database->fetchOne("SELECT * FROM users WHERE id = ? LIMIT 1", [$_SESSION['user_id']]);
        
        if ($user) {
            echo "<span style='color: green;'>✓ تم جلب بيانات المستخدم بنجاح</span><br>";
            echo "<strong>الاسم:</strong> " . htmlspecialchars($user['full_name']) . "<br>";
            echo "<strong>البريد:</strong> " . htmlspecialchars($user['email']) . "<br>";
            echo "<strong>الحالة:</strong> " . $user['account_status'] . "<br>";
        } else {
            echo "<span style='color: red;'>✗ لم يتم العثور على بيانات المستخدم</span><br>";
        }
        
    } else {
        echo "<span style='color: red;'>✗ كائن قاعدة البيانات غير متاح</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// فحص الجداول المطلوبة
echo "<h2>3. فحص الجداول المطلوبة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$required_tables = ['users', 'courses', 'enrollments', 'notifications', 'activity_logs'];

foreach ($required_tables as $table) {
    try {
        $result = $database->fetchOne("SELECT COUNT(*) as count FROM {$table}");
        $count = $result['count'] ?? 0;
        echo "<span style='color: green;'>✓ جدول {$table}: {$count} سجل</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ جدول {$table}: غير موجود أو خطأ</span><br>";
    }
}

echo "</div>";

// اختبار الاستعلامات
echo "<h2>4. اختبار الاستعلامات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$user_id = $_SESSION['user_id'];

// اختبار إحصائيات الطالب
try {
    $enrolled_count = $database->fetchOne(
        "SELECT COUNT(*) as count FROM enrollments WHERE user_id = ?", 
        [$user_id]
    )['count'] ?? 0;
    echo "<span style='color: green;'>✓ الدورات المسجل بها: {$enrolled_count}</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في جلب الدورات المسجل بها</span><br>";
}

// اختبار الإشعارات
try {
    $notifications = $database->fetchAll(
        "SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC LIMIT 5",
        [$user_id]
    );
    
    if (is_array($notifications)) {
        $count = count($notifications);
        echo "<span style='color: green;'>✓ الإشعارات: {$count} إشعار غير مقروء</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ الإشعارات: لا توجد إشعارات أو جدول غير موجود</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في جلب الإشعارات: " . $e->getMessage() . "</span><br>";
}

// اختبار النشاط الأخير
try {
    $activity = $database->fetchAll(
        "SELECT * FROM activity_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 5",
        [$user_id]
    );
    
    if (is_array($activity)) {
        $count = count($activity);
        echo "<span style='color: green;'>✓ النشاط الأخير: {$count} نشاط</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ النشاط الأخير: لا يوجد نشاط أو جدول غير موجود</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في جلب النشاط: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// اختبار الدوال
echo "<h2>5. اختبار الدوال:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

echo "<strong>is_logged_in():</strong> " . (is_logged_in() ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>') . "<br>";
echo "<strong>is_student():</strong> " . (is_student() ? '<span style="color: green;">نعم</span>' : '<span style="color: red;">لا</span>') . "<br>";

echo "</div>";

// روابط الاختبار
echo "<h2>6. اختبار الوصول:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if (is_student()) {
    echo "<a href='student/dashboard.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة الطالب الأصلية</a>";
} else {
    echo "<span style='color: red;'>لست مسجل كطالب</span><br>";
}

echo "<a href='test_session.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الجلسة</a>";
echo "<a href='simple_login.php' style='background: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>تسجيل دخول</a>";

echo "</div>";

// إنشاء جداول تجريبية إذا لم تكن موجودة
echo "<h2>7. إنشاء جداول تجريبية:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if (isset($_POST['create_tables'])) {
    try {
        // إنشاء جدول الإشعارات إذا لم يكن موجود
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                is_read TINYINT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        // إنشاء جدول سجل النشاط إذا لم يكن موجود
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                activity_description TEXT NOT NULL,
                ip_address VARCHAR(45),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        echo "<span style='color: green;'>✓ تم إنشاء الجداول بنجاح</span><br>";
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ خطأ في إنشاء الجداول: " . $e->getMessage() . "</span><br>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='create_tables' style='background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 5px;'>إنشاء الجداول المفقودة</button>";
echo "</form>";

echo "</div>";

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='student/dashboard.php'>لوحة الطالب</a></li>";
echo "<li><a href='test_session.php'>اختبار الجلسة</a></li>";
echo "<li><a href='debug_login.php'>تشخيص تسجيل الدخول</a></li>";
echo "<li><a href='simple_login.php'>تسجيل دخول بسيط</a></li>";
echo "</ul>";
?>
