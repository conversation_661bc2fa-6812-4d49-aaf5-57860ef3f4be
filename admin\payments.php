<?php
/**
 * إدارة المدفوعات
 * Payments Management
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
if (!is_logged_in() || !is_admin()) {
    redirect(SITE_URL . '/auth/login.php');
}

$page_title = 'إدارة المدفوعات';

// الحصول على جميع المدفوعات
$payments = [];
if ($db) {
    try {
        $result = $database->fetchAll("
            SELECT p.*,
                   u.full_name, u.email, u.student_id,
                   c.course_name, c.instructor_name,
                   e.enrollment_date
            FROM payments p
            JOIN users u ON p.user_id = u.id
            JOIN courses c ON p.course_id = c.id
            JOIN enrollments e ON p.enrollment_id = e.id
            ORDER BY p.payment_date DESC
        ");

        // التأكد من أن النتيجة مصفوفة
        if (is_array($result)) {
            $payments = $result;
        } else {
            $payments = [];
        }
    } catch (Exception $e) {
        // إذا لم يكن جدول المدفوعات موجود، أنشئ بيانات وهمية
        $payments = [];
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column">
                <h5 class="text-white mb-4 text-center">
                    <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>إدارة الدورات
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>إدارة الطلاب
                    </a>
                    <a class="nav-link" href="enrollments.php">
                        <i class="fas fa-user-graduate me-2"></i>التسجيلات
                    </a>
                    <a class="nav-link active" href="payments.php">
                        <i class="fas fa-credit-card me-2"></i>المدفوعات
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>التقارير
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-credit-card me-2"></i>إدارة المدفوعات</h2>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                            <h5><?php echo is_array($payments) ? count($payments) : 0; ?></h5>
                            <small class="text-muted">إجمالي المدفوعات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5><?php echo is_array($payments) ? count(array_filter($payments, function($p) { return $p['payment_status'] === 'completed'; })) : 0; ?></h5>
                            <small class="text-muted">مدفوعات مكتملة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h5><?php echo is_array($payments) ? count(array_filter($payments, function($p) { return $p['payment_status'] === 'pending'; })) : 0; ?></h5>
                            <small class="text-muted">في الانتظار</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-dollar-sign fa-2x text-info mb-2"></i>
                            <h5><?php echo is_array($payments) ? number_format(array_sum(array_column(array_filter($payments, function($p) { return $p['payment_status'] === 'completed'; }), 'amount')), 2) : '0.00'; ?></h5>
                            <small class="text-muted">إجمالي الإيرادات</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- جدول المدفوعات -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>المعرف</th>
                                    <th>الطالب</th>
                                    <th>الدورة</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الدفع</th>
                                    <th>معرف المعاملة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($payments)): ?>
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i><br>
                                            لا توجد مدفوعات بعد
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($payments as $payment): ?>
                                        <tr>
                                            <td><?php echo $payment['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($payment['full_name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($payment['email']); ?></small><br>
                                                <small class="text-muted">ID: <?php echo htmlspecialchars($payment['student_id']); ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($payment['course_name']); ?></strong><br>
                                                <small class="text-muted">المدرب: <?php echo htmlspecialchars($payment['instructor_name']); ?></small>
                                            </td>
                                            <td>
                                                <span class="h6 text-success"><?php echo number_format($payment['amount'], 2); ?> USD</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo ucfirst($payment['payment_method']); ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $status_colors = [
                                                    'pending' => 'warning',
                                                    'completed' => 'success',
                                                    'failed' => 'danger',
                                                    'refunded' => 'secondary'
                                                ];
                                                $status_text = [
                                                    'pending' => 'في الانتظار',
                                                    'completed' => 'مكتمل',
                                                    'failed' => 'فاشل',
                                                    'refunded' => 'مسترد'
                                                ];
                                                ?>
                                                <span class="badge bg-<?php echo $status_colors[$payment['payment_status']] ?? 'secondary'; ?>">
                                                    <?php echo $status_text[$payment['payment_status']] ?? $payment['payment_status']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y/m/d H:i', strtotime($payment['payment_date'])); ?></td>
                                            <td>
                                                <?php if ($payment['transaction_id']): ?>
                                                    <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- إحصائيات تفصيلية -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-pie me-2"></i>توزيع المدفوعات حسب الحالة</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $status_counts = [];
                            if (is_array($payments)) {
                                foreach ($payments as $payment) {
                                    $status = $payment['payment_status'];
                                    $status_counts[$status] = ($status_counts[$status] ?? 0) + 1;
                                }
                            }
                            ?>
                            <?php if (!empty($status_counts)): ?>
                                <?php foreach ($status_counts as $status => $count): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo $status_text[$status] ?? $status; ?></span>
                                        <span class="badge bg-<?php echo $status_colors[$status] ?? 'secondary'; ?>"><?php echo $count; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">لا توجد بيانات</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="fas fa-chart-bar me-2"></i>طرق الدفع</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $method_counts = [];
                            if (is_array($payments)) {
                                foreach ($payments as $payment) {
                                    $method = $payment['payment_method'];
                                    $method_counts[$method] = ($method_counts[$method] ?? 0) + 1;
                                }
                            }
                            ?>
                            <?php if (!empty($method_counts)): ?>
                                <?php foreach ($method_counts as $method => $count): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span><?php echo ucfirst($method); ?></span>
                                        <span class="badge bg-info"><?php echo $count; ?></span>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted">لا توجد بيانات</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php include '../includes/footer.php'; ?>
