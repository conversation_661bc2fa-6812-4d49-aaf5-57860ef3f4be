<?php
/**
 * تصفح الدورات المتاحة
 * Browse Available Courses
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'تصفح الدورات';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على الدورات المتاحة
try {
    $courses = $database->fetchAll("
        SELECT c.*, 
               COUNT(e.id) as enrolled_count,
               (SELECT COUNT(*) FROM enrollments WHERE user_id = ? AND course_id = c.id) as is_enrolled
        FROM courses c 
        LEFT JOIN enrollments e ON c.id = e.course_id 
        WHERE c.is_active = 1 
        GROUP BY c.id 
        ORDER BY c.created_at DESC
    ", [$user_id]);
    
    if (!is_array($courses)) {
        $courses = [];
    }
} catch (Exception $e) {
    $courses = [];
}

// فلترة الدورات
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';

if ($search || $category) {
    $filtered_courses = array_filter($courses, function($course) use ($search, $category) {
        $match_search = empty($search) || 
                       stripos($course['course_name'], $search) !== false || 
                       stripos($course['instructor_name'], $search) !== false ||
                       stripos($course['description'], $search) !== false;
        
        $match_category = empty($category) || $course['category'] === $category;
        
        return $match_search && $match_category;
    });
    $courses = $filtered_courses;
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h5>تصفح الدورات</h5>
                <p class="small mb-0">اكتشف دورات جديدة</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link active" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-search me-2"></i>تصفح الدورات</h2>
            </div>
            
            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="ابحث في اسم الدورة أو المدرب...">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label">التصنيف</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع التصنيفات</option>
                                <option value="programming" <?php echo $category === 'programming' ? 'selected' : ''; ?>>البرمجة</option>
                                <option value="design" <?php echo $category === 'design' ? 'selected' : ''; ?>>التصميم</option>
                                <option value="business" <?php echo $category === 'business' ? 'selected' : ''; ?>>الأعمال</option>
                                <option value="marketing" <?php echo $category === 'marketing' ? 'selected' : ''; ?>>التسويق</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- الدورات -->
            <?php if (empty($courses)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-book fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">لا توجد دورات متاحة</h4>
                    <p class="text-muted">لم يتم العثور على دورات تطابق معايير البحث</p>
                    <a href="browse-courses.php" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>عرض جميع الدورات
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($courses as $course): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 course-card">
                            <?php if ($course['course_image']): ?>
                                <img src="../assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                                     class="card-img-top" style="height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-book fa-3x text-white"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-user me-1"></i>
                                    <?php echo htmlspecialchars($course['instructor_name']); ?>
                                </p>
                                
                                <?php if ($course['description']): ?>
                                    <p class="card-text"><?php echo htmlspecialchars(substr($course['description'], 0, 100)) . '...'; ?></p>
                                <?php endif; ?>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <span class="h5 text-success mb-0">
                                            <?php echo number_format($course['price'], 2); ?> USD
                                        </span>
                                        <small class="text-muted">
                                            <i class="fas fa-users me-1"></i>
                                            <?php echo $course['enrolled_count']; ?> طالب
                                        </small>
                                    </div>
                                    
                                    <?php if ($course['is_enrolled'] > 0): ?>
                                        <button class="btn btn-success w-100" disabled>
                                            <i class="fas fa-check me-2"></i>مسجل بالفعل
                                        </button>
                                    <?php else: ?>
                                        <a href="../payment/process.php?course_id=<?php echo $course['id']; ?>" 
                                           class="btn btn-primary w-100">
                                            <i class="fas fa-shopping-cart me-2"></i>سجل الآن
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.course-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.main-content {
    padding: 20px;
}
</style>

<?php include '../includes/footer.php'; ?>
