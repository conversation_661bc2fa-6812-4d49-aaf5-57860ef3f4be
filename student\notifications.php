<?php
/**
 * إشعارات الطالب
 * Student Notifications
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'الإشعارات';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];
$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $notification_id = (int)($_POST['notification_id'] ?? 0);
    
    if ($action === 'mark_read' && $notification_id > 0) {
        try {
            $updated = $database->update(
                'notifications', 
                ['is_read' => 1], 
                'id = ? AND user_id = ?', 
                [$notification_id, $user_id]
            );
            
            if ($updated) {
                $message = 'تم تحديد الإشعار كمقروء';
                $message_type = 'success';
            }
        } catch (Exception $e) {
            $message = 'حدث خطأ أثناء تحديث الإشعار';
            $message_type = 'error';
        }
    }
    
    elseif ($action === 'mark_all_read') {
        try {
            $updated = $database->update(
                'notifications', 
                ['is_read' => 1], 
                'user_id = ? AND is_read = 0', 
                [$user_id]
            );
            
            $message = 'تم تحديد جميع الإشعارات كمقروءة';
            $message_type = 'success';
        } catch (Exception $e) {
            $message = 'حدث خطأ أثناء تحديث الإشعارات';
            $message_type = 'error';
        }
    }
    
    elseif ($action === 'delete' && $notification_id > 0) {
        try {
            $deleted = $database->delete('notifications', 'id = ? AND user_id = ?', [$notification_id, $user_id]);
            
            if ($deleted) {
                $message = 'تم حذف الإشعار';
                $message_type = 'success';
            }
        } catch (Exception $e) {
            $message = 'حدث خطأ أثناء حذف الإشعار';
            $message_type = 'error';
        }
    }
}

// الحصول على الإشعارات
try {
    $notifications = $database->fetchAll("
        SELECT * FROM notifications 
        WHERE user_id = ? 
        ORDER BY created_at DESC
    ", [$user_id]);
    
    if (!is_array($notifications)) {
        $notifications = [];
    }
} catch (Exception $e) {
    $notifications = [];
}

// إحصائيات الإشعارات
$unread_count = 0;
$total_count = count($notifications);

foreach ($notifications as $notification) {
    if (!$notification['is_read']) {
        $unread_count++;
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-bell fa-3x mb-3"></i>
                <h5>الإشعارات</h5>
                <p class="small mb-0"><?php echo $unread_count; ?> غير مقروء</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link active" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                    <?php if ($unread_count > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo $unread_count; ?></span>
                    <?php endif; ?>
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell me-2"></i>الإشعارات</h2>
                
                <?php if ($unread_count > 0): ?>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="mark_all_read">
                    <button type="submit" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-check-double me-2"></i>تحديد الكل كمقروء
                    </button>
                </form>
                <?php endif; ?>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-bell fa-2x text-primary mb-2"></i>
                            <h4><?php echo $total_count; ?></h4>
                            <small class="text-muted">إجمالي الإشعارات</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                            <h4><?php echo $unread_count; ?></h4>
                            <small class="text-muted">غير مقروء</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قائمة الإشعارات -->
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                    <h4 class="text-muted">لا توجد إشعارات</h4>
                    <p class="text-muted">لم تتلق أي إشعارات بعد</p>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-body p-0">
                        <?php foreach ($notifications as $notification): ?>
                        <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?> p-4 border-bottom">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <h6 class="mb-0 me-2"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="badge bg-primary">جديد</span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <p class="mb-2 text-muted"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo format_date($notification['created_at'], 'Y/m/d H:i'); ?>
                                    </small>
                                </div>
                                
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if (!$notification['is_read']): ?>
                                        <li>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="dropdown-item">
                                                    <i class="fas fa-check me-2"></i>تحديد كمقروء
                                                </button>
                                            </form>
                                        </li>
                                        <?php endif; ?>
                                        <li>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="fas fa-trash me-2"></i>حذف
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.main-content {
    padding: 20px;
}

.notification-item {
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notification-item:last-child {
    border-bottom: none !important;
}
</style>

<?php include '../includes/footer.php'; ?>
