<?php
/**
 * فحص حالة النظام
 * System Status Check
 */

echo "<h2>فحص حالة النظام</h2>";

// 1. فحص ملف الإعدادات
echo "<h3>1. ملف الإعدادات:</h3>";
if (file_exists('config/database.php')) {
    echo "<span style='color: green;'>✓ ملف config/database.php موجود</span><br>";
    
    // تضمين الملف
    try {
        require_once 'config/database.php';
        echo "<span style='color: green;'>✓ تم تحميل ملف الإعدادات بنجاح</span><br>";
        
        // فحص الاتصال
        if (isset($database) && $database instanceof Database) {
            echo "<span style='color: green;'>✓ تم إنشاء كائن قاعدة البيانات</span><br>";
            
            if (isset($db) && $db) {
                echo "<span style='color: green;'>✓ الاتصال بقاعدة البيانات نشط</span><br>";
                
                // فحص الجداول
                try {
                    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                    if (!empty($tables)) {
                        echo "<span style='color: green;'>✓ الجداول موجودة: " . implode(', ', $tables) . "</span><br>";
                        
                        // فحص جدول المستخدمين
                        $admin_count = $db->query("SELECT COUNT(*) FROM users WHERE user_type = 'admin'")->fetchColumn();
                        if ($admin_count > 0) {
                            echo "<span style='color: green;'>✓ يوجد {$admin_count} مدير في النظام</span><br>";
                        } else {
                            echo "<span style='color: orange;'>⚠ لا يوجد مدراء في النظام</span><br>";
                        }
                        
                    } else {
                        echo "<span style='color: orange;'>⚠ قاعدة البيانات فارغة</span><br>";
                    }
                } catch (Exception $e) {
                    echo "<span style='color: red;'>✗ خطأ في فحص الجداول: " . $e->getMessage() . "</span><br>";
                }
                
            } else {
                echo "<span style='color: red;'>✗ فشل الاتصال بقاعدة البيانات</span><br>";
            }
        } else {
            echo "<span style='color: red;'>✗ لم يتم إنشاء كائن قاعدة البيانات</span><br>";
        }
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ خطأ في تحميل ملف الإعدادات: " . $e->getMessage() . "</span><br>";
    }
} else {
    echo "<span style='color: red;'>✗ ملف config/database.php غير موجود</span><br>";
}

// 2. فحص الملفات المطلوبة
echo "<h3>2. الملفات المطلوبة:</h3>";
$required_files = [
    'index.php' => 'الصفحة الرئيسية',
    'auth/login.php' => 'صفحة تسجيل الدخول',
    'auth/register.php' => 'صفحة التسجيل',
    'admin/index.php' => 'لوحة تحكم المدير',
    'student/dashboard.php' => 'لوحة تحكم الطالب'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✓ {$description} ({$file})</span><br>";
    } else {
        echo "<span style='color: red;'>✗ {$description} ({$file}) غير موجود</span><br>";
    }
}

// 3. فحص المجلدات
echo "<h3>3. المجلدات:</h3>";
$required_dirs = [
    'assets' => 'مجلد الأصول',
    'assets/uploads' => 'مجلد الرفع',
    'config' => 'مجلد الإعدادات',
    'includes' => 'مجلد الملفات المشتركة'
];

foreach ($required_dirs as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'قابل للكتابة' : 'غير قابل للكتابة';
        $color = is_writable($dir) ? 'green' : 'orange';
        echo "<span style='color: {$color};'>✓ {$description} ({$dir}) - {$writable}</span><br>";
    } else {
        echo "<span style='color: red;'>✗ {$description} ({$dir}) غير موجود</span><br>";
    }
}

// 4. فحص امتدادات PHP
echo "<h3>4. امتدادات PHP:</h3>";
$required_extensions = [
    'pdo_mysql' => 'MySQL PDO',
    'gd' => 'معالجة الصور',
    'fileinfo' => 'معلومات الملفات',
    'session' => 'الجلسات'
];

foreach ($required_extensions as $ext => $description) {
    if ($ext === 'session') {
        $loaded = function_exists('session_start');
    } else {
        $loaded = extension_loaded($ext);
    }
    
    if ($loaded) {
        echo "<span style='color: green;'>✓ {$description} ({$ext})</span><br>";
    } else {
        echo "<span style='color: red;'>✗ {$description} ({$ext}) غير متوفر</span><br>";
    }
}

echo "<hr>";
echo "<h3>الإجراءات المقترحة:</h3>";
echo "<ul>";
echo "<li><a href='install_simple.php'>تشغيل معالج التثبيت المبسط</a></li>";
echo "<li><a href='test_db.php'>اختبار قاعدة البيانات</a></li>";
echo "<li><a href='index.php'>الذهاب إلى الصفحة الرئيسية</a></li>";
echo "</ul>";
?>
