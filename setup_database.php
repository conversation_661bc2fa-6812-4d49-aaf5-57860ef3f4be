<?php
/**
 * إعداد قاعدة البيانات السريع
 * Quick Database Setup
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'elzam_training_system';
$db_user = 'root';
$db_pass = '';

$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['auto'])) {
    try {
        // الاتصال بخادم MySQL
        $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$db_name}`");
        
        // قراءة وتنفيذ سكريبت قاعدة البيانات
        $sql_file = 'database/schema.sql';
        if (file_exists($sql_file)) {
            $sql = file_get_contents($sql_file);
            
            // تقسيم الاستعلامات
            $queries = explode(';', $sql);
            
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query) && !preg_match('/^(--|#)/', $query)) {
                    $pdo->exec($query);
                }
            }
        }
        
        $success = true;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - الزام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .setup-container {
            max-width: 600px;
            margin: 100px auto;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-card">
                <div class="setup-header">
                    <i class="fas fa-database fa-3x mb-3"></i>
                    <h2>إعداد قاعدة البيانات</h2>
                    <p class="mb-0">نظام إدارة الدورات التدريبية - الزام</p>
                </div>
                
                <div class="p-4">
                    <?php if ($success): ?>
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h4>تم إعداد قاعدة البيانات بنجاح!</h4>
                            <p>تم إنشاء قاعدة البيانات والجداول بنجاح.</p>
                            
                            <div class="d-grid gap-2 mt-4">
                                <a href="index.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>الذهاب إلى الصفحة الرئيسية
                                </a>
                                <a href="install.php" class="btn btn-outline-primary">
                                    <i class="fas fa-cog me-2"></i>إكمال إعداد النظام
                                </a>
                            </div>
                        </div>
                        
                    <?php elseif ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>خطأ:</strong> <?php echo htmlspecialchars($error); ?>
                        </div>
                        
                        <div class="d-grid">
                            <button onclick="location.reload()" class="btn btn-warning">
                                <i class="fas fa-redo me-2"></i>إعادة المحاولة
                            </button>
                        </div>
                        
                    <?php else: ?>
                        <div class="text-center">
                            <h4 class="mb-4">إعداد قاعدة البيانات</h4>
                            <p class="text-muted mb-4">
                                سيتم إنشاء قاعدة البيانات والجداول المطلوبة للنظام.
                            </p>
                            
                            <div class="mb-4">
                                <strong>إعدادات قاعدة البيانات:</strong><br>
                                <small class="text-muted">
                                    الخادم: <?php echo $db_host; ?><br>
                                    قاعدة البيانات: <?php echo $db_name; ?><br>
                                    المستخدم: <?php echo $db_user; ?>
                                </small>
                            </div>
                            
                            <form method="POST" class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-play me-2"></i>بدء الإعداد
                                </button>
                            </form>
                            
                            <div class="mt-3">
                                <a href="test_db.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-vial me-2"></i>اختبار الاتصال
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
