<?php
/**
 * إدارة الدورات - نسخة بسيطة بدون Modal
 * Course Management - Simple Version without Modal
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'إدارة الدورات - نسخة بسيطة';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_admin()) {
    redirect(SITE_URL . '/auth/login.php');
}

$message = '';
$message_type = 'info';

// معالجة إضافة الدورة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_course') {
    try {
        // التحقق من البيانات المطلوبة
        $course_name = trim($_POST['course_name'] ?? '');
        $instructor_name = trim($_POST['instructor_name'] ?? '');
        $duration_hours = (int)($_POST['duration_hours'] ?? 0);
        $price = (float)($_POST['price'] ?? 0);
        
        if (empty($course_name) || empty($instructor_name) || $duration_hours <= 0 || $price < 0) {
            $message = 'يرجى ملء جميع البيانات المطلوبة بشكل صحيح';
            $message_type = 'error';
        } else {
            $course_data = [
                'course_name' => $course_name,
                'course_description' => trim($_POST['course_description'] ?? ''),
                'instructor_name' => $instructor_name,
                'duration_hours' => $duration_hours,
                'price' => $price,
                'start_date' => $_POST['start_date'] ?: null,
                'end_date' => $_POST['end_date'] ?: null,
                'max_students' => (int)($_POST['max_students'] ?? 50),
                'course_link' => trim($_POST['course_link'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $course_id = $database->insert('courses', $course_data);
            
            if ($course_id) {
                $message = 'تم إضافة الدورة بنجاح! معرف الدورة: ' . $course_id;
                $message_type = 'success';
                
                // مسح النموذج بعد النجاح
                $_POST = [];
            } else {
                $message = 'فشل في إضافة الدورة';
                $message_type = 'error';
            }
        }
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء إضافة الدورة: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// معالجة تغيير حالة الدورة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'toggle_status') {
    try {
        $course_id = (int)($_POST['course_id'] ?? 0);
        $current_status = (int)($_POST['current_status'] ?? 0);
        $new_status = $current_status ? 0 : 1;
        
        if ($course_id <= 0) {
            $message = 'معرف الدورة غير صحيح';
            $message_type = 'error';
        } else {
            $result = $database->update('courses', ['is_active' => $new_status], 'id = ?', [$course_id]);
            
            if ($result) {
                $message = $new_status ? 'تم تفعيل الدورة' : 'تم إلغاء تفعيل الدورة';
                $message_type = 'success';
            } else {
                $message = 'فشل في تغيير حالة الدورة';
                $message_type = 'error';
            }
        }
    } catch (Exception $e) {
        $message = 'حدث خطأ أثناء تغيير حالة الدورة: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// الحصول على جميع الدورات
$courses = [];
if ($db) {
    try {
        $courses = $database->fetchAll("SELECT * FROM courses ORDER BY created_at DESC");
        if (!is_array($courses)) {
            $courses = [];
        }
    } catch (Exception $e) {
        $courses = [];
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column">
                <h5 class="text-white mb-4 text-center">
                    <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                </h5>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>الرئيسية
                    </a>
                    <a class="nav-link active" href="courses_simple.php">
                        <i class="fas fa-book me-2"></i>إدارة الدورات (بسيط)
                    </a>
                    <a class="nav-link" href="courses.php">
                        <i class="fas fa-book me-2"></i>إدارة الدورات (أصلي)
                    </a>
                    <a class="nav-link" href="students.php">
                        <i class="fas fa-users me-2"></i>إدارة الطلاب
                    </a>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book me-2"></i>إدارة الدورات - نسخة بسيطة</h2>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : $message_type; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- نموذج إضافة دورة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة دورة جديدة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="addCourseForm">
                        <input type="hidden" name="action" value="add_course">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الدورة *</label>
                                <input type="text" class="form-control" name="course_name" 
                                       value="<?php echo htmlspecialchars($_POST['course_name'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المدرب *</label>
                                <input type="text" class="form-control" name="instructor_name" 
                                       value="<?php echo htmlspecialchars($_POST['instructor_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">وصف الدورة</label>
                            <textarea class="form-control" name="course_description" rows="3"><?php echo htmlspecialchars($_POST['course_description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">المدة (ساعة) *</label>
                                <input type="number" class="form-control" name="duration_hours" min="1" 
                                       value="<?php echo htmlspecialchars($_POST['duration_hours'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">السعر (USD) *</label>
                                <input type="number" class="form-control" name="price" step="0.01" min="0" 
                                       value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">الحد الأقصى للطلاب</label>
                                <input type="number" class="form-control" name="max_students" min="1" 
                                       value="<?php echo htmlspecialchars($_POST['max_students'] ?? '50'); ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ البداية</label>
                                <input type="date" class="form-control" name="start_date" 
                                       value="<?php echo htmlspecialchars($_POST['start_date'] ?? ''); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ النهاية</label>
                                <input type="date" class="form-control" name="end_date" 
                                       value="<?php echo htmlspecialchars($_POST['end_date'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">رابط الدورة</label>
                            <input type="url" class="form-control" name="course_link" placeholder="https://..." 
                                   value="<?php echo htmlspecialchars($_POST['course_link'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                   <?php echo (isset($_POST['is_active']) || !isset($_POST['action'])) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">
                                دورة نشطة
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-plus me-2"></i>إضافة الدورة
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="clearForm()">
                            <i class="fas fa-eraser me-2"></i>مسح النموذج
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- جدول الدورات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الدورات (<?php echo count($courses); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($courses)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i><br>
                            لا توجد دورات مضافة بعد
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المعرف</th>
                                        <th>اسم الدورة</th>
                                        <th>المدرب</th>
                                        <th>المدة</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($courses as $course): ?>
                                        <tr>
                                            <td><?php echo $course['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($course['course_name']); ?></strong>
                                                <?php if ($course['course_description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($course['course_description'], 0, 50)); ?>...</small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($course['instructor_name']); ?></td>
                                            <td><?php echo $course['duration_hours']; ?> ساعة</td>
                                            <td><?php echo number_format($course['price'], 2); ?> USD</td>
                                            <td>
                                                <span class="badge bg-<?php echo $course['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $course['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('Y/m/d H:i', strtotime($course['created_at'])); ?></td>
                                            <td>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="course_id" value="<?php echo $course['id']; ?>">
                                                    <input type="hidden" name="current_status" value="<?php echo $course['is_active']; ?>">
                                                    <button type="submit" class="btn btn-outline-<?php echo $course['is_active'] ? 'warning' : 'success'; ?> btn-sm">
                                                        <i class="fas fa-<?php echo $course['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                        <?php echo $course['is_active'] ? 'إيقاف' : 'تفعيل'; ?>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// معالجة نموذج إضافة الدورة
document.getElementById('addCourseForm').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    // تعطيل الزر وتغيير النص
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
    submitBtn.disabled = true;
    
    // إعادة تفعيل الزر بعد 5 ثواني في حالة عدم إعادة تحميل الصفحة
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});

// مسح النموذج
function clearForm() {
    document.getElementById('addCourseForm').reset();
    document.getElementById('is_active').checked = true;
}

// عرض رسالة نجاح
<?php if ($message_type === 'success'): ?>
setTimeout(() => {
    if (typeof showSuccess === 'function') {
        showSuccess('<?php echo addslashes($message); ?>');
    }
}, 500);
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
