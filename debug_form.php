<?php
/**
 * تشخيص مشكلة النموذج
 * Debug Form Issue
 */

echo "<h1>تشخيص مشكلة النموذج</h1>";

// عرض جميع البيانات المرسلة
echo "<h2>البيانات المرسلة:</h2>";
echo "<h3>POST Data:</h3>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h3>GET Data:</h3>";
echo "<pre>" . print_r($_GET, true) . "</pre>";

echo "<h3>REQUEST Method:</h3>";
echo "<pre>" . $_SERVER['REQUEST_METHOD'] . "</pre>";

echo "<h3>Content Type:</h3>";
echo "<pre>" . ($_SERVER['CONTENT_TYPE'] ?? 'Not set') . "</pre>";

// اختبار قاعدة البيانات
require_once 'config/config.php';

echo "<h2>اختبار قاعدة البيانات:</h2>";

if ($db) {
    echo "<span style='color: green;'>✓ الاتصال بقاعدة البيانات يعمل</span><br>";
    
    // اختبار إدراج بسيط
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_insert'])) {
        try {
            $test_data = [
                'course_name' => 'اختبار النموذج - ' . date('H:i:s'),
                'instructor_name' => 'مدرب اختبار',
                'duration_hours' => 10,
                'price' => 50.00,
                'max_students' => 20,
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            echo "<h3>بيانات الاختبار:</h3>";
            echo "<pre>" . print_r($test_data, true) . "</pre>";
            
            $result = $database->insert('courses', $test_data);
            
            if ($result) {
                echo "<span style='color: green;'>✓ تم إدراج البيانات بنجاح! ID: {$result}</span><br>";
            } else {
                echo "<span style='color: red;'>✗ فشل في إدراج البيانات</span><br>";
            }
            
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗ خطأ: " . $e->getMessage() . "</span><br>";
        }
    }
    
} else {
    echo "<span style='color: red;'>✗ فشل الاتصال بقاعدة البيانات</span><br>";
}

// اختبار النموذج المباشر
echo "<h2>اختبار النموذج المباشر:</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_course') {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>تم استلام بيانات إضافة الدورة:</h3>";
    
    $course_name = trim($_POST['course_name'] ?? '');
    $instructor_name = trim($_POST['instructor_name'] ?? '');
    $duration_hours = (int)($_POST['duration_hours'] ?? 0);
    $price = (float)($_POST['price'] ?? 0);
    
    echo "اسم الدورة: " . htmlspecialchars($course_name) . "<br>";
    echo "اسم المدرب: " . htmlspecialchars($instructor_name) . "<br>";
    echo "المدة: " . $duration_hours . " ساعة<br>";
    echo "السعر: " . $price . " USD<br>";
    
    if (empty($course_name) || empty($instructor_name) || $duration_hours <= 0 || $price < 0) {
        echo "<span style='color: red;'>✗ بيانات غير صحيحة</span><br>";
    } else {
        echo "<span style='color: green;'>✓ البيانات صحيحة</span><br>";
        
        try {
            $course_data = [
                'course_name' => $course_name,
                'course_description' => trim($_POST['course_description'] ?? ''),
                'instructor_name' => $instructor_name,
                'duration_hours' => $duration_hours,
                'price' => $price,
                'start_date' => $_POST['start_date'] ?: null,
                'end_date' => $_POST['end_date'] ?: null,
                'max_students' => (int)($_POST['max_students'] ?? 50),
                'course_link' => trim($_POST['course_link'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $course_id = $database->insert('courses', $course_data);
            
            if ($course_id) {
                echo "<span style='color: green;'>✓ تم إضافة الدورة بنجاح! ID: {$course_id}</span><br>";
            } else {
                echo "<span style='color: red;'>✗ فشل في إضافة الدورة</span><br>";
            }
            
        } catch (Exception $e) {
            echo "<span style='color: red;'>✗ خطأ في إضافة الدورة: " . $e->getMessage() . "</span><br>";
        }
    }
    
    echo "</div>";
}
?>

<!-- نموذج اختبار بسيط -->
<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>اختبار إدراج مباشر:</h3>
    <button type="submit" name="test_insert" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        اختبار إدراج مباشر
    </button>
</form>

<!-- نموذج إضافة دورة مطابق للأصلي -->
<form method="POST" style="background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <h3>نموذج إضافة دورة (مطابق للأصلي):</h3>
    <input type="hidden" name="action" value="add_course">
    
    <div style="margin: 10px 0;">
        <label>اسم الدورة:</label><br>
        <input type="text" name="course_name" required style="width: 300px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>اسم المدرب:</label><br>
        <input type="text" name="instructor_name" required style="width: 300px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>وصف الدورة:</label><br>
        <textarea name="course_description" style="width: 300px; height: 80px; padding: 8px;"></textarea>
    </div>
    
    <div style="margin: 10px 0;">
        <label>المدة (ساعة):</label><br>
        <input type="number" name="duration_hours" min="1" required style="width: 150px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>السعر (USD):</label><br>
        <input type="number" name="price" step="0.01" min="0" required style="width: 150px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>الحد الأقصى للطلاب:</label><br>
        <input type="number" name="max_students" min="1" value="50" required style="width: 150px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>تاريخ البداية:</label><br>
        <input type="date" name="start_date" style="width: 200px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>تاريخ النهاية:</label><br>
        <input type="date" name="end_date" style="width: 200px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>رابط الدورة:</label><br>
        <input type="url" name="course_link" placeholder="https://..." style="width: 400px; padding: 8px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>
            <input type="checkbox" name="is_active" checked> دورة نشطة
        </label>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        إضافة الدورة
    </button>
</form>

<div style="margin: 20px 0;">
    <h3>روابط مفيدة:</h3>
    <ul>
        <li><a href="admin/courses.php">صفحة إدارة الدورات الأصلية</a></li>
        <li><a href="test_add_course.php">اختبار إضافة دورة</a></li>
        <li><a href="create_tables.php">إنشاء الجداول</a></li>
    </ul>
</div>
