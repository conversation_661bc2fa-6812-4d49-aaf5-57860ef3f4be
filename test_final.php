<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; direction: rtl; }
        .test-card { margin: 20px 0; padding: 20px; border-radius: 10px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-5">
            <i class="fas fa-check-circle text-success"></i>
            اختبار النظام النهائي
        </h1>

        <?php
        require_once 'config/config.php';

        // اختبار قاعدة البيانات
        echo '<div class="test-card">';
        echo '<h3><i class="fas fa-database me-2"></i>اختبار قاعدة البيانات</h3>';
        
        if ($db) {
            echo '<div class="alert alert-success">✓ الاتصال بقاعدة البيانات يعمل</div>';
            
            // اختبار الجداول
            try {
                $tables = ['users', 'courses', 'enrollments', 'payments', 'certificates', 'notifications'];
                foreach ($tables as $table) {
                    $result = $database->fetchOne("SELECT COUNT(*) as count FROM {$table}");
                    if ($result !== false) {
                        echo "<div class='alert alert-info'>✓ جدول {$table}: {$result['count']} سجل</div>";
                    }
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-warning">⚠ بعض الجداول غير موجودة - <a href="create_tables.php">إنشاء الجداول</a></div>';
            }
        } else {
            echo '<div class="alert alert-danger">✗ فشل الاتصال بقاعدة البيانات</div>';
        }
        echo '</div>';

        // اختبار الملفات
        echo '<div class="test-card">';
        echo '<h3><i class="fas fa-file me-2"></i>اختبار الملفات</h3>';
        
        $files = [
            'config/config.php' => 'ملف الإعدادات',
            'includes/header.php' => 'ملف الرأس',
            'includes/footer.php' => 'ملف التذييل',
            'auth/login.php' => 'صفحة تسجيل الدخول',
            'auth/register.php' => 'صفحة التسجيل',
            'admin/courses.php' => 'إدارة الدورات',
            'student/dashboard.php' => 'لوحة الطالب'
        ];
        
        foreach ($files as $file => $name) {
            if (file_exists($file)) {
                echo "<div class='alert alert-success'>✓ {$name}</div>";
            } else {
                echo "<div class='alert alert-danger'>✗ {$name} - غير موجود</div>";
            }
        }
        echo '</div>';

        // اختبار المجلدات
        echo '<div class="test-card">';
        echo '<h3><i class="fas fa-folder me-2"></i>اختبار المجلدات</h3>';
        
        $folders = [
            'assets' => 'مجلد الأصول',
            'assets/css' => 'مجلد CSS',
            'assets/js' => 'مجلد JavaScript',
            'assets/uploads' => 'مجلد الرفع'
        ];
        
        foreach ($folders as $folder => $name) {
            if (is_dir($folder)) {
                echo "<div class='alert alert-success'>✓ {$name}</div>";
            } else {
                echo "<div class='alert alert-warning'>⚠ {$name} - غير موجود - <a href='create_folders.php'>إنشاء المجلدات</a></div>";
            }
        }
        echo '</div>';

        // اختبار الحسابات
        echo '<div class="test-card">';
        echo '<h3><i class="fas fa-users me-2"></i>اختبار الحسابات</h3>';
        
        if ($db) {
            try {
                $admin = $database->fetchOne("SELECT * FROM users WHERE user_type = 'admin' LIMIT 1");
                $student = $database->fetchOne("SELECT * FROM users WHERE user_type = 'student' LIMIT 1");
                
                if ($admin) {
                    echo '<div class="alert alert-success">✓ حساب مدير موجود: ' . htmlspecialchars($admin['email']) . '</div>';
                } else {
                    echo '<div class="alert alert-warning">⚠ لا يوجد حساب مدير - <a href="create_admin.php">إنشاء حساب مدير</a></div>';
                }
                
                if ($student) {
                    echo '<div class="alert alert-success">✓ حساب طالب موجود: ' . htmlspecialchars($student['email']) . '</div>';
                } else {
                    echo '<div class="alert alert-info">ℹ لا يوجد حساب طالب - يمكن التسجيل من صفحة التسجيل</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">✗ خطأ في فحص الحسابات: ' . $e->getMessage() . '</div>';
            }
        }
        echo '</div>';
        ?>

        <!-- روابط الاختبار -->
        <div class="test-card">
            <h3><i class="fas fa-link me-2"></i>روابط الاختبار</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>صفحات المصادقة:</h5>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="auth/login.php" class="text-decoration-none">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="auth/register.php" class="text-decoration-none">
                                <i class="fas fa-user-plus me-2"></i>تسجيل جديد
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>لوحات التحكم:</h5>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="admin/index.php" class="text-decoration-none">
                                <i class="fas fa-cogs me-2"></i>لوحة الإدارة
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="student/dashboard.php" class="text-decoration-none">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة الطالب
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- بيانات تسجيل الدخول -->
        <div class="test-card">
            <h3><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول التجريبية</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">حساب المدير</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>البريد:</strong> <EMAIL></p>
                            <p><strong>كلمة المرور:</strong> admin123</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">حساب الطالب</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>البريد:</strong> <EMAIL></p>
                            <p><strong>كلمة المرور:</strong> student123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات الإدارة -->
        <div class="test-card">
            <h3><i class="fas fa-tools me-2"></i>أدوات الإدارة</h3>
            <div class="btn-group" role="group">
                <a href="create_tables.php" class="btn btn-primary">
                    <i class="fas fa-database me-2"></i>إنشاء الجداول
                </a>
                <a href="create_admin.php" class="btn btn-success">
                    <i class="fas fa-user-shield me-2"></i>إنشاء حسابات
                </a>
                <a href="create_folders.php" class="btn btn-warning">
                    <i class="fas fa-folder-plus me-2"></i>إنشاء المجلدات
                </a>
            </div>
        </div>

        <div class="text-center mt-5">
            <h4 class="text-success">
                <i class="fas fa-check-circle me-2"></i>
                النظام جاهز للاستخدام!
            </h4>
            <p class="text-muted">جميع المكونات تعمل بشكل صحيح</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
