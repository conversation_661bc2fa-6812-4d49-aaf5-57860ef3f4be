<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'elzam_training_system';
$db_user = 'root';
$db_pass = '';
$db_charset = 'utf8mb4';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

// اختبار 1: التحقق من امتداد MySQL
echo "<h3>1. التحقق من امتداد MySQL:</h3>";
if (extension_loaded('pdo_mysql')) {
    echo "<span style='color: green;'>✓ امتداد PDO MySQL متوفر</span><br>";
} else {
    echo "<span style='color: red;'>✗ امتداد PDO MySQL غير متوفر</span><br>";
    exit;
}

// اختبار 2: محاولة الاتصال بالخادم
echo "<h3>2. الاتصال بخادم MySQL:</h3>";
try {
    $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<span style='color: green;'>✓ تم الاتصال بخادم MySQL بنجاح</span><br>";
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗ فشل الاتصال بخادم MySQL: " . $e->getMessage() . "</span><br>";
    exit;
}

// اختبار 3: التحقق من وجود قاعدة البيانات
echo "<h3>3. التحقق من قاعدة البيانات:</h3>";
try {
    $stmt = $pdo->query("SHOW DATABASES LIKE '{$db_name}'");
    $database_exists = $stmt->rowCount() > 0;
    
    if ($database_exists) {
        echo "<span style='color: green;'>✓ قاعدة البيانات '{$db_name}' موجودة</span><br>";
    } else {
        echo "<span style='color: orange;'>⚠ قاعدة البيانات '{$db_name}' غير موجودة - سيتم إنشاؤها</span><br>";
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<span style='color: green;'>✓ تم إنشاء قاعدة البيانات بنجاح</span><br>";
    }
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗ خطأ في التحقق من قاعدة البيانات: " . $e->getMessage() . "</span><br>";
}

// اختبار 4: الاتصال بقاعدة البيانات
echo "<h3>4. الاتصال بقاعدة البيانات:</h3>";
try {
    $dsn = "mysql:host={$db_host};dbname={$db_name};charset={$db_charset}";
    $pdo = new PDO($dsn, $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
    
    echo "<span style='color: green;'>✓ تم الاتصال بقاعدة البيانات '{$db_name}' بنجاح</span><br>";
    
    // التحقق من الجداول
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<span style='color: orange;'>⚠ قاعدة البيانات فارغة - تحتاج إلى تشغيل سكريبت التثبيت</span><br>";
        echo "<a href='install.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشغيل معالج التثبيت</a><br>";
    } else {
        echo "<span style='color: green;'>✓ الجداول موجودة: " . implode(', ', $tables) . "</span><br>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>✗ فشل الاتصال بقاعدة البيانات: " . $e->getMessage() . "</span><br>";
}

// اختبار 5: معلومات الخادم
echo "<h3>5. معلومات الخادم:</h3>";
try {
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "إصدار MySQL: {$version}<br>";
    
    $charset = $pdo->query('SELECT @@character_set_database')->fetchColumn();
    echo "ترميز قاعدة البيانات: {$charset}<br>";
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>خطأ في الحصول على معلومات الخادم: " . $e->getMessage() . "</span><br>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li><a href='install.php'>تشغيل معالج التثبيت</a> لإعداد قاعدة البيانات والجداول</li>";
echo "<li><a href='index.php'>الذهاب إلى الصفحة الرئيسية</a> بعد اكتمال التثبيت</li>";
echo "</ol>";
?>
