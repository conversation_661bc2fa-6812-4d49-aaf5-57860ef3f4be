<?php
/**
 * لوحة تحكم المشرف
 * Admin Dashboard
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'لوحة تحكم المشرف';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_admin()) {
    redirect(SITE_URL . '/auth/login.php');
}

// الحصول على الإحصائيات العامة
$stats = [
    'total_students' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'")['count'] ?? 0,
    'pending_students' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student' AND account_status = 'pending'")['count'] ?? 0,
    'approved_students' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student' AND account_status = 'approved'")['count'] ?? 0,
    'total_courses' => $database->fetchOne("SELECT COUNT(*) as count FROM courses")['count'] ?? 0,
    'active_courses' => $database->fetchOne("SELECT COUNT(*) as count FROM courses WHERE is_active = 1")['count'] ?? 0,
    'total_enrollments' => $database->fetchOne("SELECT COUNT(*) as count FROM enrollments")['count'] ?? 0,
    'completed_payments' => $database->fetchOne("SELECT COUNT(*) as count FROM payments WHERE payment_status = 'completed'")['count'] ?? 0,
    'pending_payments' => $database->fetchOne("SELECT COUNT(*) as count FROM payments WHERE payment_status = 'pending'")['count'] ?? 0,
    'total_revenue' => $database->fetchOne("SELECT SUM(amount) as total FROM payments WHERE payment_status = 'completed'")['total'] ?? 0
];

// الحصول على الطلاب المعلقين
$pending_students = $database->fetchAll(
    "SELECT * FROM users WHERE user_type = 'student' AND account_status = 'pending' ORDER BY created_at DESC LIMIT 10"
);

// الحصول على أحدث التسجيلات
$recent_enrollments = $database->fetchAll(
    "SELECT e.*, u.full_name, u.student_id, c.course_name 
     FROM enrollments e 
     JOIN users u ON e.user_id = u.id 
     JOIN courses c ON e.course_id = c.id 
     ORDER BY e.enrollment_date DESC 
     LIMIT 10"
);

// الحصول على النشاط الأخير
$recent_activity = $database->fetchAll(
    "SELECT al.*, u.full_name 
     FROM activity_logs al 
     LEFT JOIN users u ON al.user_id = u.id 
     ORDER BY al.created_at DESC 
     LIMIT 15"
);

// بيانات الرسم البياني - التسجيلات الشهرية
$monthly_enrollments = $database->fetchAll(
    "SELECT DATE_FORMAT(enrollment_date, '%Y-%m') as month, COUNT(*) as count 
     FROM enrollments 
     WHERE enrollment_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH) 
     GROUP BY DATE_FORMAT(enrollment_date, '%Y-%m') 
     ORDER BY month"
);

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-user-shield fa-3x mb-3"></i>
                <h5>لوحة الإدارة</h5>
                <p class="small mb-0">مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link active" href="index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="students.php">
                    <i class="fas fa-users me-2"></i>إدارة الطلاب
                    <?php if ($stats['pending_students'] > 0): ?>
                        <span class="badge bg-warning ms-2"><?php echo $stats['pending_students']; ?></span>
                    <?php endif; ?>
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>إدارة الدورات
                </a>
                <a class="nav-link" href="enrollments.php">
                    <i class="fas fa-user-graduate me-2"></i>التسجيلات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-2"></i>التقارير
                </a>
                <a class="nav-link" href="logs.php">
                    <i class="fas fa-history me-2"></i>سجل النشاط
                </a>
                <a class="nav-link" href="settings.php">
                    <i class="fas fa-cogs me-2"></i>الإعدادات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">لوحة تحكم المشرف</h2>
                    <p class="text-muted mb-0">نظرة عامة على النظام والإحصائيات</p>
                </div>
                <div class="text-end">
                    <div id="current-time" class="text-muted small"></div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="fw-bold text-primary"><?php echo number_format($stats['total_students']); ?></h4>
                            <p class="text-muted mb-0">إجمالي الطلاب</p>
                            <small class="text-success">
                                <i class="fas fa-check-circle"></i> <?php echo $stats['approved_students']; ?> مفعل
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-book fa-2x text-success mb-2"></i>
                            <h4 class="fw-bold text-success"><?php echo number_format($stats['total_courses']); ?></h4>
                            <p class="text-muted mb-0">إجمالي الدورات</p>
                            <small class="text-success">
                                <i class="fas fa-play-circle"></i> <?php echo $stats['active_courses']; ?> نشط
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-user-graduate fa-2x text-warning mb-2"></i>
                            <h4 class="fw-bold text-warning"><?php echo number_format($stats['total_enrollments']); ?></h4>
                            <p class="text-muted mb-0">إجمالي التسجيلات</p>
                            <small class="text-info">
                                <i class="fas fa-chart-line"></i> نمو مستمر
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-dollar-sign fa-2x text-info mb-2"></i>
                            <h4 class="fw-bold text-info"><?php echo format_currency($stats['total_revenue']); ?></h4>
                            <p class="text-muted mb-0">إجمالي الإيرادات</p>
                            <small class="text-warning">
                                <i class="fas fa-clock"></i> <?php echo $stats['pending_payments']; ?> معلق
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>إجراءات سريعة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="courses.php?action=add" class="btn btn-primary w-100">
                                        <i class="fas fa-plus me-2"></i>إضافة دورة جديدة
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="students.php?status=pending" class="btn btn-warning w-100">
                                        <i class="fas fa-user-clock me-2"></i>مراجعة الطلاب المعلقين
                                        <?php if ($stats['pending_students'] > 0): ?>
                                            <span class="badge bg-light text-dark ms-2"><?php echo $stats['pending_students']; ?></span>
                                        <?php endif; ?>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="reports.php" class="btn btn-info w-100">
                                        <i class="fas fa-chart-bar me-2"></i>عرض التقارير
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <a href="settings.php" class="btn btn-secondary w-100">
                                        <i class="fas fa-cogs me-2"></i>إعدادات النظام
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Pending Students -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-user-clock me-2"></i>الطلاب المعلقين
                            </h5>
                            <a href="students.php?status=pending" class="btn btn-sm btn-outline-primary">
                                عرض الكل
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($pending_students)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد طلبات معلقة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>الطالب</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($pending_students, 0, 5) as $student): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($student['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($student['student_id']); ?></small>
                                                </td>
                                                <td><?php echo format_date($student['created_at'], 'Y/m/d'); ?></td>
                                                <td>
                                                    <a href="students.php?action=review&id=<?php echo $student['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Enrollments -->
                <div class="col-lg-6 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-user-graduate me-2"></i>أحدث التسجيلات
                            </h5>
                            <a href="enrollments.php" class="btn btn-sm btn-outline-primary">
                                عرض الكل
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_enrollments)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-user-graduate fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد تسجيلات حديثة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>الطالب</th>
                                                <th>الدورة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($recent_enrollments, 0, 5) as $enrollment): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($enrollment['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($enrollment['student_id']); ?></small>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars($enrollment['course_name']); ?></small>
                                                </td>
                                                <td><?php echo format_date($enrollment['enrollment_date'], 'm/d'); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts and Activity -->
            <div class="row">
                <!-- Enrollment Chart -->
                <div class="col-lg-8 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>التسجيلات الشهرية
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="enrollmentChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>النشاط الأخير
                            </h5>
                        </div>
                        <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                            <?php if (empty($recent_activity)): ?>
                                <p class="text-muted text-center">لا يوجد نشاط مسجل</p>
                            <?php else: ?>
                                <div class="timeline">
                                    <?php foreach (array_slice($recent_activity, 0, 10) as $activity): ?>
                                    <div class="timeline-item mb-3">
                                        <div class="d-flex">
                                            <div class="timeline-marker me-3">
                                                <i class="fas fa-circle text-primary small"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <p class="mb-1 small">
                                                    <?php echo htmlspecialchars($activity['activity_description']); ?>
                                                </p>
                                                <small class="text-muted">
                                                    <?php echo $activity['full_name'] ? htmlspecialchars($activity['full_name']) : 'مجهول'; ?>
                                                    - <?php echo format_date($activity['created_at'], 'H:i'); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// رسم بياني للتسجيلات الشهرية
const ctx = document.getElementById('enrollmentChart').getContext('2d');
const enrollmentData = <?php echo json_encode($monthly_enrollments); ?>;

const labels = enrollmentData.map(item => {
    const date = new Date(item.month + '-01');
    return date.toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
});

const data = enrollmentData.map(item => item.count);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'التسجيلات',
            data: data,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>

<style>
.timeline-marker {
    width: 20px;
    text-align: center;
    margin-top: 2px;
}

.timeline-content {
    flex: 1;
}
</style>

<?php include '../includes/footer.php'; ?>
