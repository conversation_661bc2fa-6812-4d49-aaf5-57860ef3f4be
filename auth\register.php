<?php
/**
 * صفحة التسجيل
 * Registration Page
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'تسجيل حساب جديد';

// إعادة توجيه المستخدمين المسجلين
if (is_logged_in()) {
    if (is_admin()) {
        redirect(SITE_URL . '/admin/index.php');
    } else {
        redirect(SITE_URL . '/student/dashboard.php');
    }
}

$error_message = '';
$success_message = '';
$form_data = [];

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // جمع البيانات
        $form_data = [
            'student_id' => sanitize_input($_POST['student_id'] ?? ''),
            'full_name' => sanitize_input($_POST['full_name'] ?? ''),
            'email' => sanitize_input($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'university_level' => sanitize_input($_POST['university_level'] ?? ''),
            'specialization' => sanitize_input($_POST['specialization'] ?? ''),
        ];
        
        // التحقق من البيانات
        $validation_errors = [];
        
        if (empty($form_data['student_id'])) {
            $validation_errors[] = 'رقم الطالب مطلوب';
        } elseif (strlen($form_data['student_id']) < 6) {
            $validation_errors[] = 'رقم الطالب يجب أن يكون 6 أرقام على الأقل';
        }
        
        if (empty($form_data['full_name'])) {
            $validation_errors[] = 'الاسم الكامل مطلوب';
        } elseif (strlen($form_data['full_name']) < 3) {
            $validation_errors[] = 'الاسم يجب أن يكون 3 أحرف على الأقل';
        }
        
        if (empty($form_data['email'])) {
            $validation_errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!validate_email($form_data['email'])) {
            $validation_errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (empty($form_data['password'])) {
            $validation_errors[] = 'كلمة المرور مطلوبة';
        } elseif (strlen($form_data['password']) < PASSWORD_MIN_LENGTH) {
            $validation_errors[] = 'كلمة المرور يجب أن تكون ' . PASSWORD_MIN_LENGTH . ' أحرف على الأقل';
        }
        
        if ($form_data['password'] !== $form_data['confirm_password']) {
            $validation_errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
        }
        
        if (empty($form_data['university_level'])) {
            $validation_errors[] = 'المرحلة الجامعية مطلوبة';
        }
        
        if (empty($form_data['specialization'])) {
            $validation_errors[] = 'التخصص مطلوب';
        }
        
        // التحقق من رفع صورة البطاقة الجامعية
        if (!isset($_FILES['university_card']) || $_FILES['university_card']['error'] !== UPLOAD_ERR_OK) {
            $validation_errors[] = 'صورة البطاقة الجامعية مطلوبة';
        }
        
        if (empty($validation_errors)) {
            try {
                // التحقق من عدم وجود المستخدم مسبقاً
                $existing_user = $database->fetchOne("SELECT id FROM users WHERE email = ? OR student_id = ?", [$form_data['email'], $form_data['student_id']]);

                if ($existing_user) {
                    $error_message = 'البريد الإلكتروني أو رقم الطالب مستخدم مسبقاً';
                } else {
                    // رفع صورة البطاقة الجامعية
                    $upload_dir = '../assets/uploads/university_cards/';

                    // إنشاء المجلد إذا لم يكن موجوداً
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $file = $_FILES['university_card'];
                    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

                    if (in_array($file_extension, $allowed_extensions) && $file['size'] <= 5 * 1024 * 1024) {
                        $filename = uniqid() . '_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . $filename;

                        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                            // إدراج المستخدم في قاعدة البيانات مباشرة
                            $user_data = [
                                'student_id' => $form_data['student_id'],
                                'full_name' => $form_data['full_name'],
                                'email' => $form_data['email'],
                                'password' => password_hash($form_data['password'], PASSWORD_DEFAULT),
                                'university_level' => $form_data['university_level'],
                                'specialization' => $form_data['specialization'],
                                'university_card_image' => 'university_cards/' . $filename,
                                'user_type' => 'student',
                                'account_status' => 'pending',
                                'is_active' => 1,
                                'created_at' => date('Y-m-d H:i:s')
                            ];

                            $user_id = $database->insert('users', $user_data);

                            if ($user_id) {
                                $success_message = 'تم التسجيل بنجاح! حسابك قيد المراجعة وسيتم إشعارك عند الموافقة عليه.';
                                $form_data = []; // مسح البيانات
                            } else {
                                $error_message = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
                                // حذف الصورة المرفوعة
                                if (file_exists($upload_path)) {
                                    unlink($upload_path);
                                }
                            }
                        } else {
                            $error_message = 'فشل في رفع صورة البطاقة الجامعية';
                        }
                    } else {
                        $error_message = 'صورة البطاقة الجامعية يجب أن تكون بصيغة JPG أو PNG وأقل من 5 ميجابايت';
                    }
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.';
                error_log("Registration error: " . $e->getMessage());
            }
    } else {
        $error_message = implode('<br>', $validation_errors);
    }
}

include '../includes/header.php';
?>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <i class="fas fa-user-plus fa-3x mb-3"></i>
                    <h3 class="mb-0">تسجيل حساب جديد</h3>
                    <p class="mb-0">انضم إلى منصة الزام التعليمية</p>
                </div>
                
                <div class="card-body p-5">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                            <hr>
                            <a href="login.php" class="btn btn-success">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </a>
                        </div>
                    <?php else: ?>
                    
                    <form method="POST" action="" enctype="multipart/form-data" id="registerForm">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="student_id" class="form-label">
                                    <i class="fas fa-id-card me-2"></i>رقم الطالب *
                                </label>
                                <input type="text" class="form-control" id="student_id" name="student_id" 
                                       value="<?php echo htmlspecialchars($form_data['student_id'] ?? ''); ?>" 
                                       placeholder="مثال: 202012345" required>
                                <div class="form-text">رقم الطالب الجامعي الخاص بك</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">
                                    <i class="fas fa-user me-2"></i>الاسم الكامل *
                                </label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($form_data['full_name'] ?? ''); ?>" 
                                       placeholder="الاسم الثلاثي أو الرباعي" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>البريد الإلكتروني *
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>" 
                                   placeholder="<EMAIL>" required>
                            <div class="form-text">يفضل استخدام البريد الجامعي</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>كلمة المرور *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           minlength="<?php echo PASSWORD_MIN_LENGTH; ?>" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    يجب أن تكون <?php echo PASSWORD_MIN_LENGTH; ?> أحرف على الأقل
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>تأكيد كلمة المرور *
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="university_level" class="form-label">
                                    <i class="fas fa-graduation-cap me-2"></i>المرحلة الجامعية *
                                </label>
                                <select class="form-select" id="university_level" name="university_level" required>
                                    <option value="">اختر المرحلة</option>
                                    <option value="السنة الأولى" <?php echo ($form_data['university_level'] ?? '') === 'السنة الأولى' ? 'selected' : ''; ?>>السنة الأولى</option>
                                    <option value="السنة الثانية" <?php echo ($form_data['university_level'] ?? '') === 'السنة الثانية' ? 'selected' : ''; ?>>السنة الثانية</option>
                                    <option value="السنة الثالثة" <?php echo ($form_data['university_level'] ?? '') === 'السنة الثالثة' ? 'selected' : ''; ?>>السنة الثالثة</option>
                                    <option value="السنة الرابعة" <?php echo ($form_data['university_level'] ?? '') === 'السنة الرابعة' ? 'selected' : ''; ?>>السنة الرابعة</option>
                                    <option value="السنة الخامسة" <?php echo ($form_data['university_level'] ?? '') === 'السنة الخامسة' ? 'selected' : ''; ?>>السنة الخامسة</option>
                                    <option value="دراسات عليا" <?php echo ($form_data['university_level'] ?? '') === 'دراسات عليا' ? 'selected' : ''; ?>>دراسات عليا</option>
                                    <option value="خريج" <?php echo ($form_data['university_level'] ?? '') === 'خريج' ? 'selected' : ''; ?>>خريج</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="specialization" class="form-label">
                                    <i class="fas fa-book me-2"></i>التخصص *
                                </label>
                                <input type="text" class="form-control" id="specialization" name="specialization" 
                                       value="<?php echo htmlspecialchars($form_data['specialization'] ?? ''); ?>" 
                                       placeholder="مثال: علوم الحاسب" required>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label for="university_card" class="form-label">
                                <i class="fas fa-image me-2"></i>صورة البطاقة الجامعية *
                            </label>
                            <input type="file" class="form-control" id="university_card" name="university_card" 
                                   accept="image/*" required>
                            <div class="form-text">
                                يجب أن تكون الصورة واضحة وبصيغة JPG أو PNG (الحد الأقصى 5 ميجابايت)
                            </div>
                            <img id="image-preview" class="mt-2 img-thumbnail" style="display: none; max-width: 200px;">
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="<?php echo SITE_URL; ?>/terms.php" target="_blank">شروط الاستخدام</a> 
                                    و <a href="<?php echo SITE_URL; ?>/privacy.php" target="_blank">سياسة الخصوصية</a>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                        </button>
                    </form>
                    
                    <?php endif; ?>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">لديك حساب بالفعل؟</p>
                        <a href="login.php" class="btn btn-outline-primary mt-2">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// إظهار/إخفاء كلمة المرور
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// معاينة الصورة
document.getElementById('university_card').addEventListener('change', function() {
    const file = this.files[0];
    const preview = document.getElementById('image-preview');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// تحسين تجربة المستخدم
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إنشاء الحساب...';
    submitBtn.disabled = true;
});
</script>

<?php include '../includes/footer.php'; ?>
