<?php
/**
 * مدفوعات الطالب
 * Student Payments
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'المدفوعات';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على المدفوعات
try {
    $payments = $database->fetchAll("
        SELECT p.*, c.course_name, c.instructor_name, e.enrollment_date
        FROM payments p
        JOIN courses c ON p.course_id = c.id
        JOIN enrollments e ON p.enrollment_id = e.id
        WHERE p.user_id = ?
        ORDER BY p.payment_date DESC
    ", [$user_id]);
    
    if (!is_array($payments)) {
        $payments = [];
    }
} catch (Exception $e) {
    $payments = [];
}

// الحصول على المدفوعات المعلقة
try {
    $pending_payments = $database->fetchAll("
        SELECT e.*, c.course_name, c.instructor_name, c.price
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        WHERE e.user_id = ? 
        AND e.payment_status = 'pending'
        ORDER BY e.enrollment_date DESC
    ", [$user_id]);
    
    if (!is_array($pending_payments)) {
        $pending_payments = [];
    }
} catch (Exception $e) {
    $pending_payments = [];
}

// حساب الإحصائيات
$total_paid = 0;
$completed_payments = 0;
$failed_payments = 0;

foreach ($payments as $payment) {
    if ($payment['payment_status'] === 'completed') {
        $total_paid += $payment['amount'];
        $completed_payments++;
    } elseif ($payment['payment_status'] === 'failed') {
        $failed_payments++;
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-credit-card fa-3x mb-3"></i>
                <h5>المدفوعات</h5>
                <p class="small mb-0"><?php echo count($payments); ?> معاملة</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link active" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-credit-card me-2"></i>مدفوعاتي</h2>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                            <h4><?php echo number_format($total_paid, 2); ?> USD</h4>
                            <small class="text-muted">إجمالي المدفوع</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4><?php echo $completed_payments; ?></h4>
                            <small class="text-muted">مدفوعات مكتملة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4><?php echo count($pending_payments); ?></h4>
                            <small class="text-muted">في الانتظار</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                            <h4><?php echo $failed_payments; ?></h4>
                            <small class="text-muted">فاشلة</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- المدفوعات المعلقة -->
            <?php if (!empty($pending_payments)): ?>
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>مدفوعات معلقة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الدورة</th>
                                    <th>المدرب</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pending_payments as $pending): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($pending['course_name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($pending['instructor_name']); ?></td>
                                    <td>
                                        <span class="h6 text-danger"><?php echo number_format($pending['price'], 2); ?> USD</span>
                                    </td>
                                    <td><?php echo format_date($pending['enrollment_date'], 'Y/m/d'); ?></td>
                                    <td>
                                        <a href="../payment/process.php?course_id=<?php echo $pending['course_id']; ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-credit-card me-2"></i>ادفع الآن
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- تاريخ المدفوعات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>تاريخ المدفوعات</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($payments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مدفوعات بعد</h5>
                            <p class="text-muted">لم تقم بأي عمليات دفع حتى الآن</p>
                            <a href="browse-courses.php" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>تصفح الدورات
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المعرف</th>
                                        <th>الدورة</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الدفع</th>
                                        <th>معرف المعاملة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($payments as $payment): ?>
                                    <tr>
                                        <td><?php echo $payment['id']; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($payment['course_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($payment['instructor_name']); ?></small>
                                        </td>
                                        <td>
                                            <span class="h6 text-success"><?php echo number_format($payment['amount'], 2); ?> USD</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo ucfirst($payment['payment_method']); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'pending' => 'warning',
                                                'completed' => 'success',
                                                'failed' => 'danger',
                                                'refunded' => 'secondary'
                                            ];
                                            $status_text = [
                                                'pending' => 'في الانتظار',
                                                'completed' => 'مكتمل',
                                                'failed' => 'فاشل',
                                                'refunded' => 'مسترد'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_colors[$payment['payment_status']] ?? 'secondary'; ?>">
                                                <?php echo $status_text[$payment['payment_status']] ?? $payment['payment_status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo format_date($payment['payment_date'], 'Y/m/d H:i'); ?></td>
                                        <td>
                                            <?php if ($payment['transaction_id']): ?>
                                                <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.main-content {
    padding: 20px;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}
</style>

<?php include '../includes/footer.php'; ?>
