<?php
/**
 * شهادات الطالب
 * Student Certificates
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'الشهادات';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على الشهادات
try {
    $certificates = $database->fetchAll("
        SELECT c.*, co.course_name, co.instructor_name, e.completion_date
        FROM certificates c
        JOIN courses co ON c.course_id = co.id
        JOIN enrollments e ON c.enrollment_id = e.id
        WHERE c.user_id = ?
        ORDER BY c.issued_date DESC
    ", [$user_id]);
    
    if (!is_array($certificates)) {
        $certificates = [];
    }
} catch (Exception $e) {
    // إذا لم يكن جدول الشهادات موجود، إنشاء مصفوفة فارغة
    $certificates = [];
}

// الحصول على الدورات المكتملة بدون شهادات
try {
    $completed_courses = $database->fetchAll("
        SELECT e.*, c.course_name, c.instructor_name
        FROM enrollments e
        JOIN courses c ON e.course_id = c.id
        WHERE e.user_id = ? 
        AND e.enrollment_status = 'completed'
        AND e.id NOT IN (SELECT enrollment_id FROM certificates WHERE user_id = ?)
        ORDER BY e.completion_date DESC
    ", [$user_id, $user_id]);
    
    if (!is_array($completed_courses)) {
        $completed_courses = [];
    }
} catch (Exception $e) {
    $completed_courses = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-certificate fa-3x mb-3"></i>
                <h5>الشهادات</h5>
                <p class="small mb-0"><?php echo count($certificates); ?> شهادة</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a class="nav-link active" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-certificate me-2"></i>شهاداتي</h2>
            </div>
            
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-certificate fa-2x text-success mb-2"></i>
                            <h4><?php echo count($certificates); ?></h4>
                            <small class="text-muted">شهادات حاصل عليها</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4><?php echo count($completed_courses); ?></h4>
                            <small class="text-muted">في انتظار الشهادة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-download fa-2x text-info mb-2"></i>
                            <h4><?php echo count($certificates); ?></h4>
                            <small class="text-muted">متاح للتحميل</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الشهادات الحاصل عليها -->
            <?php if (!empty($certificates)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-award me-2"></i>الشهادات الحاصل عليها</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($certificates as $certificate): ?>
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="certificate-card">
                                <div class="certificate-header">
                                    <i class="fas fa-certificate fa-2x text-warning mb-2"></i>
                                    <h6 class="mb-0">شهادة إتمام الدورة</h6>
                                </div>
                                
                                <div class="certificate-body">
                                    <h5 class="course-name"><?php echo htmlspecialchars($certificate['course_name']); ?></h5>
                                    <p class="instructor-name">المدرب: <?php echo htmlspecialchars($certificate['instructor_name']); ?></p>
                                    
                                    <div class="certificate-details">
                                        <div class="detail-item">
                                            <strong>رقم الشهادة:</strong>
                                            <span><?php echo htmlspecialchars($certificate['certificate_number']); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <strong>تاريخ الإصدار:</strong>
                                            <span><?php echo format_date($certificate['issued_date'], 'Y/m/d'); ?></span>
                                        </div>
                                        <?php if ($certificate['completion_date']): ?>
                                        <div class="detail-item">
                                            <strong>تاريخ الإكمال:</strong>
                                            <span><?php echo format_date($certificate['completion_date'], 'Y/m/d'); ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="certificate-footer">
                                    <a href="download-certificate.php?id=<?php echo $certificate['id']; ?>" 
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-download me-2"></i>تحميل الشهادة
                                    </a>
                                    <a href="view-certificate.php?id=<?php echo $certificate['id']; ?>" 
                                       class="btn btn-outline-secondary btn-sm" target="_blank">
                                        <i class="fas fa-eye me-2"></i>عرض
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- الدورات المكتملة في انتظار الشهادة -->
            <?php if (!empty($completed_courses)): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>في انتظار إصدار الشهادة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم الدورة</th>
                                    <th>المدرب</th>
                                    <th>تاريخ الإكمال</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($completed_courses as $course): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($course['course_name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($course['instructor_name']); ?></td>
                                    <td><?php echo format_date($course['completion_date'], 'Y/m/d'); ?></td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>قيد المعالجة
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إصدار الشهادات خلال 3-5 أيام عمل من تاريخ إكمال الدورة.
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- رسالة عدم وجود شهادات -->
            <?php if (empty($certificates) && empty($completed_courses)): ?>
            <div class="text-center py-5">
                <i class="fas fa-certificate fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">لا توجد شهادات بعد</h4>
                <p class="text-muted">أكمل دوراتك للحصول على الشهادات</p>
                <a href="browse-courses.php" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.main-content {
    padding: 20px;
}

.certificate-card {
    border: 2px solid #f0c14b;
    border-radius: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
    box-shadow: 0 5px 15px rgba(240, 193, 75, 0.3);
    transition: transform 0.3s ease;
    height: 100%;
}

.certificate-card:hover {
    transform: translateY(-5px);
}

.certificate-header {
    text-align: center;
    border-bottom: 2px solid #f0c14b;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.certificate-body {
    margin-bottom: 20px;
}

.course-name {
    color: #b8860b;
    font-weight: bold;
    margin-bottom: 10px;
}

.instructor-name {
    color: #666;
    margin-bottom: 15px;
}

.certificate-details {
    background: rgba(255,255,255,0.7);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.certificate-footer {
    text-align: center;
    border-top: 2px solid #f0c14b;
    padding-top: 15px;
}

.certificate-footer .btn {
    margin: 0 5px;
}
</style>

<?php include '../includes/footer.php'; ?>
