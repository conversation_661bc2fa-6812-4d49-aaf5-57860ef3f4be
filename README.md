# نظام إدارة الدورات التدريبية - الزام

## 📋 نظرة عامة

نظام إدارة الدورات التدريبية هو منصة تعليمية متكاملة تسمح للطلاب بالتسجيل في الدورات التدريبية ومتابعة تقدمهم، مع لوحة تحكم شاملة للمشرفين لإدارة النظام.

## ✨ المميزات الرئيسية

### للطلاب:
- 📝 تسجيل حساب جديد مع رفع البطاقة الجامعية
- 🔐 نظام تسجيل دخول آمن
- 📚 تصفح الدورات المتاحة
- 💳 نظام دفع إلكتروني آمن (PayPal)
- 📊 لوحة تحكم شخصية
- 🔔 نظام إشعارات
- 📜 تحميل الشهادات

### للمشرفين:
- 👥 إدارة حسابات الطلاب (قبول/رفض)
- 📖 إدارة الدورات التدريبية
- 💰 متابعة المدفوعات
- 📈 تقارير وإحصائيات
- 🔍 سجل النشاط والأمان
- ⚙️ إعدادات النظام

## 🛠️ التقنيات المستخدمة

- **Backend:** PHP 7.4+
- **Database:** MySQL 5.7+
- **Frontend:** HTML5, CSS3, JavaScript ES6+
- **Framework:** Bootstrap 5
- **Icons:** Font Awesome 6
- **Charts:** Chart.js
- **Alerts:** SweetAlert2
- **Security:** bcrypt, CSRF Protection, SQL Injection Prevention

## 📁 هيكل المشروع

```
elzam-web-site/
├── config/
│   ├── database.php          # إعدادات قاعدة البيانات
│   └── config.php           # الإعدادات العامة
├── includes/
│   ├── header.php           # رأس الصفحة
│   ├── footer.php           # تذييل الصفحة
│   └── functions.php        # الدوال المساعدة
├── assets/
│   ├── css/                 # ملفات التنسيق
│   ├── js/                  # ملفات JavaScript
│   ├── images/              # الصور
│   └── uploads/             # الملفات المرفوعة
├── admin/
│   ├── index.php            # لوحة تحكم المشرف
│   ├── students.php         # إدارة الطلاب
│   └── courses.php          # إدارة الدورات
├── student/
│   ├── dashboard.php        # لوحة تحكم الطالب
│   ├── courses.php          # دورات الطالب
│   └── profile.php          # الملف الشخصي
├── auth/
│   ├── login.php            # تسجيل الدخول
│   ├── register.php         # التسجيل
│   └── logout.php           # تسجيل الخروج
├── payment/
│   └── process.php          # معالج الدفع
├── database/
│   └── schema.sql           # هيكل قاعدة البيانات
└── index.php                # الصفحة الرئيسية
```

## 🚀 التثبيت والإعداد

### المتطلبات:
- XAMPP أو WAMP أو LAMP
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache Web Server

### خطوات التثبيت:

1. **تحميل المشروع:**
   ```bash
   git clone https://github.com/your-repo/elzam-web-site.git
   cd elzam-web-site
   ```

2. **إعداد قاعدة البيانات:**
   - افتح phpMyAdmin
   - أنشئ قاعدة بيانات جديدة باسم `elzam_training_system`
   - استورد ملف `database/schema.sql`

3. **تكوين الإعدادات:**
   - افتح `config/database.php`
   - عدّل إعدادات قاعدة البيانات:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'elzam_training_system');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

4. **إعداد المجلدات:**
   ```bash
   mkdir assets/uploads
   mkdir assets/uploads/university_cards
   mkdir assets/uploads/course_images
   chmod 755 assets/uploads -R
   ```

5. **تشغيل الخادم:**
   - ضع المشروع في مجلد `htdocs` (XAMPP) أو `www` (WAMP)
   - ابدأ Apache و MySQL
   - افتح المتصفح وانتقل إلى `http://localhost/elzam-web-site`

## 👤 بيانات الدخول الافتراضية

### المشرف:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** admin123

### طالب تجريبي:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** student123

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية من SQL Injection
- حماية CSRF
- تتبع النشاط وسجل IP
- رفع آمن للملفات
- جلسات آمنة

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- `users` - بيانات المستخدمين
- `courses` - الدورات التدريبية
- `enrollments` - التسجيلات
- `payments` - المدفوعات
- `activity_logs` - سجل النشاط
- `notifications` - الإشعارات
- `system_settings` - إعدادات النظام

## 🎨 التخصيص

### تغيير الألوان:
عدّل المتغيرات في `includes/header.php`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
}
```

### إضافة لغات جديدة:
- أنشئ ملفات ترجمة في مجلد `lang/`
- عدّل `config/config.php` لدعم اللغات المتعددة

## 📱 التوافق

- ✅ متوافق مع جميع المتصفحات الحديثة
- ✅ تصميم متجاوب (Responsive)
- ✅ يعمل على الجوال والتابلت
- ✅ دعم اللغة العربية (RTL)

## 🔧 الصيانة

### النسخ الاحتياطي:
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p elzam_training_system > backup.sql

# نسخ احتياطي للملفات
tar -czf backup_files.tar.gz assets/uploads/
```

### تحديث النظام:
1. انسخ الملفات الجديدة
2. شغّل سكريبت التحديث (إن وجد)
3. امسح الكاش

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بقاعدة البيانات:**
   - تأكد من تشغيل MySQL
   - تحقق من إعدادات `config/database.php`

2. **مشكلة في رفع الملفات:**
   - تأكد من صلاحيات المجلد `assets/uploads`
   - تحقق من إعدادات PHP (`upload_max_filesize`)

3. **مشكلة في الجلسات:**
   - تأكد من إعدادات `session.save_path` في PHP

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع: https://elzam.com
- 📱 الهاتف: +966 50 123 4567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

## 📈 خطط المستقبل

- [ ] تطبيق جوال (React Native)
- [ ] نظام دردشة مباشرة
- [ ] تكامل مع Zoom للدروس المباشرة
- [ ] نظام تقييم الدورات
- [ ] شهادات رقمية بـ Blockchain
- [ ] ذكاء اصطناعي لتوصيات الدورات

---

**تم تطوير هذا النظام بواسطة فريق الزام التقني** 🚀
