<?php
/**
 * تشخيص مفصل لمشكلة إضافة الدورات
 * Detailed Debug for Course Addition Issue
 */

require_once 'config/config.php';

echo "<h1>تشخيص مفصل لمشكلة إضافة الدورات</h1>";

// عرض جميع البيانات
echo "<h2>1. البيانات المرسلة:</h2>";
echo "<h3>POST Data:</h3>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h3>REQUEST Method:</h3>";
echo "<pre>" . $_SERVER['REQUEST_METHOD'] . "</pre>";

echo "<h3>Content Type:</h3>";
echo "<pre>" . ($_SERVER['CONTENT_TYPE'] ?? 'Not set') . "</pre>";

// فحص قاعدة البيانات
echo "<h2>2. فحص قاعدة البيانات:</h2>";

if (!$db) {
    echo "<span style='color: red;'>❌ لا يوجد اتصال بقاعدة البيانات</span><br>";
    exit;
}

echo "<span style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</span><br>";

// فحص جدول الدورات
try {
    $table_check = $database->fetchOne("SHOW TABLES LIKE 'courses'");
    if ($table_check) {
        echo "<span style='color: green;'>✅ جدول courses موجود</span><br>";
        
        // فحص بنية الجدول
        $columns = $database->fetchAll("DESCRIBE courses");
        echo "<h3>بنية جدول courses:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . $col['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عدد الدورات الحالية
        $count = $database->fetchOne("SELECT COUNT(*) as count FROM courses");
        echo "<p><strong>عدد الدورات الحالية:</strong> " . $count['count'] . "</p>";
        
    } else {
        echo "<span style='color: red;'>❌ جدول courses غير موجود</span><br>";
        echo "<a href='create_tables.php'>إنشاء الجداول</a><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ في فحص الجدول: " . $e->getMessage() . "</span><br>";
}

// اختبار إدراج مباشر
echo "<h2>3. اختبار إدراج مباشر:</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_direct_insert'])) {
    try {
        $test_data = [
            'course_name' => 'اختبار مباشر - ' . date('H:i:s'),
            'course_description' => 'وصف اختبار',
            'instructor_name' => 'مدرب اختبار',
            'duration_hours' => 15,
            'price' => 75.00,
            'max_students' => 25,
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        echo "<h3>البيانات المرسلة للإدراج:</h3>";
        echo "<pre>" . print_r($test_data, true) . "</pre>";
        
        $result = $database->insert('courses', $test_data);
        
        if ($result) {
            echo "<span style='color: green;'>✅ تم الإدراج بنجاح! ID: {$result}</span><br>";
        } else {
            echo "<span style='color: red;'>❌ فشل الإدراج</span><br>";
        }
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>❌ خطأ في الإدراج: " . $e->getMessage() . "</span><br>";
    }
}

// محاكاة نموذج إضافة الدورة
echo "<h2>4. محاكاة نموذج إضافة الدورة:</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_course') {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔍 تم استلام بيانات إضافة الدورة:</h3>";
    
    $course_name = trim($_POST['course_name'] ?? '');
    $instructor_name = trim($_POST['instructor_name'] ?? '');
    $duration_hours = (int)($_POST['duration_hours'] ?? 0);
    $price = (float)($_POST['price'] ?? 0);
    
    echo "<p><strong>اسم الدورة:</strong> " . htmlspecialchars($course_name) . "</p>";
    echo "<p><strong>اسم المدرب:</strong> " . htmlspecialchars($instructor_name) . "</p>";
    echo "<p><strong>المدة:</strong> " . $duration_hours . " ساعة</p>";
    echo "<p><strong>السعر:</strong> " . $price . " USD</p>";
    
    // التحقق من البيانات
    if (empty($course_name) || empty($instructor_name) || $duration_hours <= 0 || $price < 0) {
        echo "<span style='color: red;'>❌ بيانات غير صحيحة</span><br>";
        
        if (empty($course_name)) echo "- اسم الدورة فارغ<br>";
        if (empty($instructor_name)) echo "- اسم المدرب فارغ<br>";
        if ($duration_hours <= 0) echo "- المدة غير صحيحة<br>";
        if ($price < 0) echo "- السعر غير صحيح<br>";
        
    } else {
        echo "<span style='color: green;'>✅ البيانات صحيحة</span><br>";
        
        try {
            $course_data = [
                'course_name' => $course_name,
                'course_description' => trim($_POST['course_description'] ?? ''),
                'instructor_name' => $instructor_name,
                'duration_hours' => $duration_hours,
                'price' => $price,
                'start_date' => $_POST['start_date'] ?: null,
                'end_date' => $_POST['end_date'] ?: null,
                'max_students' => (int)($_POST['max_students'] ?? 50),
                'course_link' => trim($_POST['course_link'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            echo "<h4>البيانات النهائية للإدراج:</h4>";
            echo "<pre>" . print_r($course_data, true) . "</pre>";
            
            $course_id = $database->insert('courses', $course_data);
            
            if ($course_id) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<span style='color: green; font-weight: bold;'>🎉 تم إضافة الدورة بنجاح!</span><br>";
                echo "<strong>معرف الدورة:</strong> {$course_id}<br>";
                echo "<strong>الوقت:</strong> " . date('Y-m-d H:i:s') . "<br>";
                echo "</div>";
                
                // التحقق من الإدراج
                $inserted_course = $database->fetchOne("SELECT * FROM courses WHERE id = ?", [$course_id]);
                if ($inserted_course) {
                    echo "<h4>تأكيد الإدراج من قاعدة البيانات:</h4>";
                    echo "<pre>" . print_r($inserted_course, true) . "</pre>";
                }
                
            } else {
                echo "<span style='color: red;'>❌ فشل في إضافة الدورة</span><br>";
                echo "<p>السبب المحتمل: مشكلة في دالة insert أو قاعدة البيانات</p>";
            }
            
        } catch (Exception $e) {
            echo "<span style='color: red;'>❌ خطأ في إضافة الدورة: " . $e->getMessage() . "</span><br>";
            echo "<p><strong>تفاصيل الخطأ:</strong></p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
    
    echo "</div>";
}

// فحص دالة insert
echo "<h2>5. فحص دالة insert:</h2>";

if (method_exists($database, 'insert')) {
    echo "<span style='color: green;'>✅ دالة insert موجودة</span><br>";
} else {
    echo "<span style='color: red;'>❌ دالة insert غير موجودة</span><br>";
}

// عرض آخر الدورات
echo "<h2>6. آخر الدورات المضافة:</h2>";
try {
    $recent_courses = $database->fetchAll("SELECT * FROM courses ORDER BY created_at DESC LIMIT 5");
    if (is_array($recent_courses) && count($recent_courses) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>اسم الدورة</th><th>المدرب</th><th>السعر</th><th>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($recent_courses as $course) {
            echo "<tr>";
            echo "<td>" . $course['id'] . "</td>";
            echo "<td>" . htmlspecialchars($course['course_name']) . "</td>";
            echo "<td>" . htmlspecialchars($course['instructor_name']) . "</td>";
            echo "<td>" . $course['price'] . "</td>";
            echo "<td>" . $course['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد دورات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب الدورات: " . $e->getMessage() . "</p>";
}
?>

<!-- نماذج الاختبار -->
<div style="margin: 30px 0;">
    <h2>7. نماذج الاختبار:</h2>
    
    <!-- اختبار إدراج مباشر -->
    <form method="POST" style="background: #e3f2fd; padding: 20px; border-radius: 5px; margin: 10px 0;">
        <h3>اختبار إدراج مباشر:</h3>
        <button type="submit" name="test_direct_insert" style="background: #2196f3; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
            اختبار إدراج مباشر
        </button>
    </form>
    
    <!-- نموذج إضافة دورة مطابق للأصلي -->
    <form method="POST" style="background: #f3e5f5; padding: 20px; border-radius: 5px; margin: 10px 0;">
        <h3>نموذج إضافة دورة (مطابق للأصلي):</h3>
        <input type="hidden" name="action" value="add_course">
        
        <div style="margin: 10px 0;">
            <label><strong>اسم الدورة:</strong></label><br>
            <input type="text" name="course_name" required style="width: 300px; padding: 8px;" placeholder="أدخل اسم الدورة">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>اسم المدرب:</strong></label><br>
            <input type="text" name="instructor_name" required style="width: 300px; padding: 8px;" placeholder="أدخل اسم المدرب">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>وصف الدورة:</strong></label><br>
            <textarea name="course_description" style="width: 300px; height: 80px; padding: 8px;" placeholder="أدخل وصف الدورة"></textarea>
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>المدة (ساعة):</strong></label><br>
            <input type="number" name="duration_hours" min="1" required style="width: 150px; padding: 8px;" placeholder="20">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>السعر (USD):</strong></label><br>
            <input type="number" name="price" step="0.01" min="0" required style="width: 150px; padding: 8px;" placeholder="99.99">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>الحد الأقصى للطلاب:</strong></label><br>
            <input type="number" name="max_students" min="1" value="50" required style="width: 150px; padding: 8px;">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>تاريخ البداية:</strong></label><br>
            <input type="date" name="start_date" style="width: 200px; padding: 8px;">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>تاريخ النهاية:</strong></label><br>
            <input type="date" name="end_date" style="width: 200px; padding: 8px;">
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>رابط الدورة:</strong></label><br>
            <input type="url" name="course_link" placeholder="https://..." style="width: 400px; padding: 8px;">
        </div>
        
        <div style="margin: 10px 0;">
            <label>
                <input type="checkbox" name="is_active" checked> دورة نشطة
            </label>
        </div>
        
        <button type="submit" style="background: #4caf50; color: white; padding: 12px 25px; border: none; border-radius: 5px; font-size: 16px;">
            🚀 إضافة الدورة
        </button>
    </form>
</div>

<div style="margin: 20px 0;">
    <h3>روابط مفيدة:</h3>
    <ul>
        <li><a href="admin/courses.php">صفحة إدارة الدورات الأصلية</a></li>
        <li><a href="admin/courses_fixed.php">صفحة إدارة الدورات المحسنة</a></li>
        <li><a href="test_add_course.php">اختبار إضافة دورة</a></li>
        <li><a href="create_tables.php">إنشاء الجداول</a></li>
    </ul>
</div>
