<?php
/**
 * الصفحة الرئيسية
 * Home Page
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

$page_title = 'الصفحة الرئيسية';

// التحقق من وجود اتصال بقاعدة البيانات
if (!$db) {
    // إعادة توجيه إلى صفحة التثبيت إذا لم يكن هناك اتصال
    header("Location: install_simple.php");
    exit();
}

// الحصول على إحصائيات الموقع
$stats = [
    'total_students' => 0,
    'total_courses' => 0,
    'total_enrollments' => 0,
    'active_courses' => 0
];

// محاولة الحصول على الإحصائيات
try {
    $result = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'");
    $stats['total_students'] = $result ? $result['count'] : 0;

    $result = $database->fetchOne("SELECT COUNT(*) as count FROM courses WHERE is_active = 1");
    $stats['total_courses'] = $result ? $result['count'] : 0;

    $result = $database->fetchOne("SELECT COUNT(*) as count FROM enrollments WHERE payment_status = 'completed'");
    $stats['total_enrollments'] = $result ? $result['count'] : 0;

    $result = $database->fetchOne("SELECT COUNT(*) as count FROM courses WHERE is_active = 1 AND start_date > NOW()");
    $stats['active_courses'] = $result ? $result['count'] : 0;
} catch (Exception $e) {
    // في حالة وجود خطأ، استخدم القيم الافتراضية
    error_log("Error fetching stats: " . $e->getMessage());
}

// الحصول على أحدث الدورات
$latest_courses = [];
try {
    $latest_courses = $database->fetchAll("SELECT * FROM courses WHERE is_active = 1 ORDER BY created_at DESC LIMIT 6");
    if (!$latest_courses) {
        $latest_courses = [];
    }
} catch (Exception $e) {
    error_log("Error fetching courses: " . $e->getMessage());
    $latest_courses = [];
}

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 70vh;">
    <div class="container">
        <div class="row align-items-center min-vh-70">
            <div class="col-lg-6 text-white">
                <h1 class="display-4 fw-bold mb-4">
                    مرحباً بك في منصة الزام التعليمية
                </h1>
                <p class="lead mb-4">
                    اكتسب مهارات جديدة وطور قدراتك من خلال دوراتنا التدريبية المتميزة. 
                    انضم إلى آلاف الطلاب واحصل على شهادات معتمدة.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <?php if (!is_logged_in()): ?>
                        <a href="auth/register.php" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-user-plus me-2"></i>ابدأ الآن
                        </a>
                        <a href="auth/login.php" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    <?php else: ?>
                        <a href="<?php echo is_admin() ? 'admin/index.php' : 'student/dashboard.php'; ?>" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                        <a href="student/courses.php" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-book me-2"></i>تصفح الدورات
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <img src="assets/images/hero-illustration.svg" alt="التعلم الإلكتروني" class="img-fluid" style="max-height: 400px;">
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h3 class="fw-bold text-primary"><?php echo number_format($stats['total_students']); ?>+</h3>
                        <p class="text-muted mb-0">طالب مسجل</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-book fa-3x text-success mb-3"></i>
                        <h3 class="fw-bold text-success"><?php echo number_format($stats['total_courses']); ?>+</h3>
                        <p class="text-muted mb-0">دورة تدريبية</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-certificate fa-3x text-warning mb-3"></i>
                        <h3 class="fw-bold text-warning"><?php echo number_format($stats['total_enrollments']); ?>+</h3>
                        <p class="text-muted mb-0">شهادة صادرة</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <i class="fas fa-play-circle fa-3x text-info mb-3"></i>
                        <h3 class="fw-bold text-info"><?php echo number_format($stats['active_courses']); ?>+</h3>
                        <p class="text-muted mb-0">دورة نشطة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold">لماذا تختار منصة الزام؟</h2>
                <p class="lead text-muted">نقدم لك أفضل تجربة تعليمية مع مميزات فريدة</p>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-chalkboard-teacher fa-3x text-primary"></i>
                    </div>
                    <h5 class="fw-bold">مدربون خبراء</h5>
                    <p class="text-muted">نخبة من أفضل المدربين والخبراء في مختلف المجالات</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-certificate fa-3x text-success"></i>
                    </div>
                    <h5 class="fw-bold">شهادات معتمدة</h5>
                    <p class="text-muted">احصل على شهادات معتمدة تضيف قيمة لسيرتك الذاتية</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-clock fa-3x text-warning"></i>
                    </div>
                    <h5 class="fw-bold">مرونة في التوقيت</h5>
                    <p class="text-muted">تعلم في الوقت الذي يناسبك مع إمكانية الوصول 24/7</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-mobile-alt fa-3x text-info"></i>
                    </div>
                    <h5 class="fw-bold">متوافق مع الجوال</h5>
                    <p class="text-muted">تصميم متجاوب يعمل على جميع الأجهزة والشاشات</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-headset fa-3x text-danger"></i>
                    </div>
                    <h5 class="fw-bold">دعم فني متميز</h5>
                    <p class="text-muted">فريق دعم فني متاح لمساعدتك في أي وقت</p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shield-alt fa-3x text-secondary"></i>
                    </div>
                    <h5 class="fw-bold">أمان وخصوصية</h5>
                    <p class="text-muted">نحمي بياناتك بأعلى معايير الأمان والخصوصية</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest Courses Section -->
<?php if (!empty($latest_courses)): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold">أحدث الدورات التدريبية</h2>
                <p class="lead text-muted">اكتشف أحدث الدورات المتاحة وابدأ رحلتك التعليمية</p>
            </div>
        </div>
        <div class="row">
            <?php foreach ($latest_courses as $course): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card course-card border-0 shadow-sm h-100">
                    <?php if ($course['course_image']): ?>
                        <img src="assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                             class="card-img-top course-image" alt="<?php echo htmlspecialchars($course['course_name']); ?>">
                    <?php else: ?>
                        <div class="card-img-top course-image bg-primary d-flex align-items-center justify-content-center">
                            <i class="fas fa-book fa-3x text-white"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                        <p class="text-muted mb-2">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($course['instructor_name']); ?>
                        </p>
                        <p class="text-muted mb-2">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo $course['duration_hours']; ?> ساعة
                        </p>
                        <p class="text-muted mb-3">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo $course['start_date'] ? date('Y/m/d', strtotime($course['start_date'])) : 'سيتم تحديده لاحقاً'; ?>
                        </p>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="h5 text-primary mb-0">
                                    <?php echo format_currency($course['price']); ?>
                                </span>
                                <small class="text-muted">
                                    <?php echo $course['current_enrolled']; ?>/<?php echo $course['max_students']; ?> طالب
                                </small>
                            </div>
                            
                            <?php if (is_logged_in() && is_student()): ?>
                                <a href="student/course-details.php?id=<?php echo $course['id']; ?>" 
                                   class="btn btn-primary w-100">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                            <?php else: ?>
                                <a href="auth/register.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-user-plus me-2"></i>سجل للمشاهدة
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="courses.php" class="btn btn-primary btn-lg">
                <i class="fas fa-book me-2"></i>عرض جميع الدورات
            </a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- CTA Section -->
<section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center text-white">
                <h2 class="display-5 fw-bold mb-4">ابدأ رحلتك التعليمية اليوم</h2>
                <p class="lead mb-4">
                    انضم إلى آلاف الطلاب الذين طوروا مهاراتهم وحققوا أهدافهم المهنية معنا
                </p>
                <?php if (!is_logged_in()): ?>
                    <a href="auth/register.php" class="btn btn-light btn-lg px-5">
                        <i class="fas fa-rocket me-2"></i>ابدأ الآن مجاناً
                    </a>
                <?php else: ?>
                    <a href="student/courses.php" class="btn btn-light btn-lg px-5">
                        <i class="fas fa-book me-2"></i>تصفح الدورات
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<style>
.min-vh-70 {
    min-height: 70vh;
}

.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-image {
    height: 200px;
    object-fit: cover;
}

.course-card {
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}
</style>

<?php include 'includes/footer.php'; ?>
