/**
 * ملف JavaScript الرئيسي لموقع الزام التعليمي
 * Main JavaScript File for Elzam Educational Platform
 */

// متغيرات عامة
let currentUserId = null;
let siteUrl = '';

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تهيئة المتغيرات
    const userIdElement = document.querySelector('[data-user-id]');
    if (userIdElement) {
        currentUserId = userIdElement.getAttribute('data-user-id');
    }
    
    const siteUrlElement = document.querySelector('[data-site-url]');
    if (siteUrlElement) {
        siteUrl = siteUrlElement.getAttribute('data-site-url');
    }
    
    // تهيئة المكونات
    initializeButtons();
    initializeFileInputs();
    initializeForms();
    initializeTooltips();
    
    // بدء التحديثات الدورية
    if (currentUserId) {
        startPeriodicUpdates();
    }
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000);
}

/**
 * تهيئة الأزرار
 */
function initializeButtons() {
    // إضافة loading state للأزرار
    const submitButtons = document.querySelectorAll('button[type="submit"], .btn-submit');
    submitButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const form = this.closest('form');
            if (form && form.checkValidity()) {
                showButtonLoading(this);
            }
        });
    });
    
    // أزرار التأكيد
    const confirmButtons = document.querySelectorAll('[data-confirm]');
    confirmButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const message = this.getAttribute('data-confirm') || 'هل أنت متأكد؟';
            confirmAction(message).then((result) => {
                if (result.isConfirmed) {
                    if (this.href) {
                        window.location.href = this.href;
                    } else if (this.onclick) {
                        this.onclick();
                    }
                }
            });
        });
    });
}

/**
 * تهيئة حقول رفع الملفات
 */
function initializeFileInputs() {
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFileUpload(this);
        });
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
                showError('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
            }
            this.classList.add('was-validated');
        });
    });
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * معالجة رفع الملفات
 */
function handleFileUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // فحص حجم الملف
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
        showError('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        input.value = '';
        return;
    }
    
    // فحص نوع الملف للصور
    if (input.accept && input.accept.includes('image/')) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showError('نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG أو PNG');
            input.value = '';
            return;
        }
        
        // عرض معاينة الصورة
        showImagePreview(file, input);
    }
    
    // عرض معلومات الملف
    showFileInfo(file, input);
}

/**
 * عرض معاينة الصورة
 */
function showImagePreview(file, input) {
    const reader = new FileReader();
    reader.onload = function(e) {
        let preview = document.getElementById('image-preview');
        if (!preview) {
            preview = document.createElement('img');
            preview.id = 'image-preview';
            preview.className = 'img-thumbnail mt-2';
            preview.style.maxWidth = '200px';
            preview.style.maxHeight = '200px';
            input.parentNode.appendChild(preview);
        }
        preview.src = e.target.result;
        preview.style.display = 'block';
    };
    reader.readAsDataURL(file);
}

/**
 * عرض معلومات الملف
 */
function showFileInfo(file, input) {
    let info = document.getElementById('file-info');
    if (!info) {
        info = document.createElement('div');
        info.id = 'file-info';
        info.className = 'small text-muted mt-1';
        input.parentNode.appendChild(info);
    }
    
    const size = (file.size / 1024 / 1024).toFixed(2);
    info.innerHTML = `
        <i class="fas fa-file me-1"></i>
        ${file.name} (${size} MB)
    `;
}

/**
 * عرض حالة التحميل للزر
 */
function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    button.disabled = true;
    
    // إعادة تفعيل الزر بعد 10 ثواني كحد أقصى
    setTimeout(() => {
        if (button.disabled) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }, 10000);
}

/**
 * تأكيد الإجراء
 */
function confirmAction(message = 'هل أنت متأكد؟') {
    return Swal.fire({
        title: 'تأكيد الإجراء',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، متأكد',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    });
}

/**
 * تأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return Swal.fire({
        title: 'تأكيد الحذف',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        reverseButtons: true
    });
}

/**
 * عرض رسالة نجاح
 */
function showSuccess(message, title = 'تم بنجاح!') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'success',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#28a745'
    });
}

/**
 * عرض رسالة خطأ
 */
function showError(message, title = 'خطأ!') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'error',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#dc3545'
    });
}

/**
 * عرض رسالة تحذير
 */
function showWarning(message, title = 'تحذير!') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'warning',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#ffc107'
    });
}

/**
 * عرض رسالة معلومات
 */
function showInfo(message, title = 'معلومات') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'info',
        confirmButtonText: 'موافق',
        confirmButtonColor: '#17a2b8'
    });
}

/**
 * تحديث الوقت
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-SA', {
        timeZone: 'Asia/Riyadh',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

/**
 * تحديث الإشعارات
 */
function updateNotifications() {
    if (!currentUserId || !siteUrl) return;

    fetch(`${siteUrl}/api/notifications.php?user_id=${currentUserId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const badge = document.querySelector('.notification-badge');
                if (data.unread_count > 0) {
                    if (badge) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'flex';
                    } else {
                        createNotificationBadge(data.unread_count);
                    }
                } else {
                    if (badge) {
                        badge.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => {
            // تجاهل أخطاء الإشعارات لتجنب إزعاج المستخدم
            console.log('Notifications update skipped:', error.message);
        });
}

/**
 * إنشاء شارة الإشعارات
 */
function createNotificationBadge(count) {
    const navLink = document.querySelector('a[href*="notifications"]');
    if (navLink && !navLink.querySelector('.notification-badge')) {
        const badge = document.createElement('span');
        badge.className = 'notification-badge';
        badge.textContent = count;
        navLink.style.position = 'relative';
        navLink.appendChild(badge);
    }
}

/**
 * بدء التحديثات الدورية
 */
function startPeriodicUpdates() {
    // تحديث الإشعارات كل 30 ثانية
    setInterval(updateNotifications, 30000);
    updateNotifications(); // تحديث فوري
}

/**
 * تهيئة جدول البيانات مع البحث
 */
function initializeDataTable(tableId) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    // إضافة حقل البحث
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'form-control mb-3';
    searchInput.placeholder = 'البحث في الجدول...';
    
    table.parentNode.insertBefore(searchInput, table);
    
    // إضافة وظيفة البحث
    searchInput.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(filter) ? '' : 'none';
        });
    });
    
    // إضافة ترقيم الصفحات إذا كان الجدول كبير
    const rows = table.querySelectorAll('tbody tr');
    if (rows.length > 10) {
        addPagination(table, 10);
    }
}

/**
 * إضافة ترقيم الصفحات
 */
function addPagination(table, rowsPerPage) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const totalPages = Math.ceil(rows.length / rowsPerPage);
    let currentPage = 1;
    
    // إخفاء جميع الصفوف
    rows.forEach(row => row.style.display = 'none');
    
    // عرض الصفحة الأولى
    showPage(1);
    
    // إنشاء أزرار الترقيم
    const pagination = document.createElement('nav');
    pagination.className = 'mt-3';
    pagination.innerHTML = `
        <ul class="pagination justify-content-center">
            <li class="page-item" id="prev-page">
                <a class="page-link" href="#" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            ${Array.from({length: totalPages}, (_, i) => `
                <li class="page-item ${i + 1 === 1 ? 'active' : ''}" data-page="${i + 1}">
                    <a class="page-link" href="#">${i + 1}</a>
                </li>
            `).join('')}
            <li class="page-item" id="next-page">
                <a class="page-link" href="#" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    `;
    
    table.parentNode.appendChild(pagination);
    
    // إضافة أحداث النقر
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        const target = e.target.closest('.page-item');
        if (!target) return;
        
        if (target.id === 'prev-page' && currentPage > 1) {
            currentPage--;
        } else if (target.id === 'next-page' && currentPage < totalPages) {
            currentPage++;
        } else if (target.dataset.page) {
            currentPage = parseInt(target.dataset.page);
        }
        
        showPage(currentPage);
        updatePaginationButtons();
    });
    
    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;
        
        rows.forEach((row, index) => {
            row.style.display = (index >= start && index < end) ? '' : 'none';
        });
    }
    
    function updatePaginationButtons() {
        const pageItems = pagination.querySelectorAll('.page-item[data-page]');
        pageItems.forEach(item => {
            item.classList.toggle('active', parseInt(item.dataset.page) === currentPage);
        });
        
        document.getElementById('prev-page').classList.toggle('disabled', currentPage === 1);
        document.getElementById('next-page').classList.toggle('disabled', currentPage === totalPages);
    }
}

// تصدير الدوال للاستخدام العام
window.confirmDelete = confirmDelete;
window.confirmAction = confirmAction;
window.showSuccess = showSuccess;
window.showError = showError;
window.showWarning = showWarning;
window.showInfo = showInfo;
window.initializeDataTable = initializeDataTable;
