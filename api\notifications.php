<?php
/**
 * API للإشعارات
 * Notifications API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    require_once '../config/config.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في تحميل الإعدادات']);
    exit;
}

// التحقق من وجود معرف المستخدم
$user_id = $_GET['user_id'] ?? null;

if (!$user_id || !is_numeric($user_id)) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المستخدم مطلوب']);
    exit;
}

// التحقق من وجود اتصال بقاعدة البيانات
if (!$db || !$database) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

try {
    // محاولة الحصول على عدد الإشعارات غير المقروءة
    $unread_count = 0;
    
    // التحقق من وجود جدول الإشعارات
    $table_exists = $database->fetchOne("SHOW TABLES LIKE 'notifications'");
    
    if ($table_exists) {
        $result = $database->fetchOne(
            "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0",
            [$user_id]
        );
        
        if ($result && isset($result['count'])) {
            $unread_count = (int)$result['count'];
        }
    }
    
    // الحصول على آخر الإشعارات
    $notifications = [];
    
    if ($table_exists) {
        $notifications = $database->fetchAll(
            "SELECT id, title, message, notification_type, is_read, created_at 
             FROM notifications 
             WHERE user_id = ? 
             ORDER BY created_at DESC 
             LIMIT 10",
            [$user_id]
        );
        
        if (!is_array($notifications)) {
            $notifications = [];
        }
    }
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'unread_count' => $unread_count,
        'notifications' => $notifications,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'خطأ في جلب الإشعارات',
        'message' => $e->getMessage()
    ]);
}
?>
