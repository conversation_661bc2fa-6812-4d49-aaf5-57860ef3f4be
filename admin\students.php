<?php
/**
 * صفحة إدارة الطلاب
 * Students Management Page
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'إدارة الطلاب';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_admin()) {
    redirect(SITE_URL . '/auth/login.php');
}

$message = '';
$message_type = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'رمز الأمان غير صحيح';
        $message_type = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        $student_id = (int)($_POST['student_id'] ?? 0);
        
        switch ($action) {
            case 'approve':
                $result = $userManager->updateAccountStatus($student_id, 'approved');
                if ($result) {
                    $message = 'تم قبول الطالب بنجاح';
                    $message_type = 'success';
                } else {
                    $message = 'فشل في قبول الطالب';
                    $message_type = 'error';
                }
                break;
                
            case 'reject':
                $reason = sanitize_input($_POST['rejection_reason'] ?? '');
                if (empty($reason)) {
                    $message = 'يجب إدخال سبب الرفض';
                    $message_type = 'error';
                } else {
                    $result = $userManager->updateAccountStatus($student_id, 'rejected', $reason);
                    if ($result) {
                        $message = 'تم رفض الطالب بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'فشل في رفض الطالب';
                        $message_type = 'error';
                    }
                }
                break;
                
            case 'activate':
                $result = $database->update('users', ['is_active' => 1], 'id = ?', [$student_id]);
                if ($result) {
                    $message = 'تم تفعيل الطالب بنجاح';
                    $message_type = 'success';
                    log_activity($_SESSION['user_id'], 'student_activate', "تفعيل الطالب رقم {$student_id}");
                }
                break;
                
            case 'deactivate':
                $result = $database->update('users', ['is_active' => 0], 'id = ?', [$student_id]);
                if ($result) {
                    $message = 'تم إلغاء تفعيل الطالب بنجاح';
                    $message_type = 'success';
                    log_activity($_SESSION['user_id'], 'student_deactivate', "إلغاء تفعيل الطالب رقم {$student_id}");
                }
                break;
        }
    }
}

// فلترة البيانات
$status_filter = $_GET['status'] ?? 'all';
$search = sanitize_input($_GET['search'] ?? '');

// بناء الاستعلام
$where_conditions = ["user_type = 'student'"];
$params = [];

if ($status_filter !== 'all') {
    $where_conditions[] = "account_status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR student_id LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على الطلاب
$students = $database->fetchAll(
    "SELECT * FROM users WHERE {$where_clause} ORDER BY created_at DESC",
    $params
);

// إحصائيات الطلاب
$student_stats = [
    'total' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'")['count'] ?? 0,
    'pending' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student' AND account_status = 'pending'")['count'] ?? 0,
    'approved' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student' AND account_status = 'approved'")['count'] ?? 0,
    'rejected' => $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE user_type = 'student' AND account_status = 'rejected'")['count'] ?? 0,
];

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h5>إدارة الطلاب</h5>
                <p class="small mb-0">مراجعة وإدارة حسابات الطلاب</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link active" href="students.php">
                    <i class="fas fa-users me-2"></i>إدارة الطلاب
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>إدارة الدورات
                </a>
                <a class="nav-link" href="enrollments.php">
                    <i class="fas fa-user-graduate me-2"></i>التسجيلات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-2"></i>التقارير
                </a>
                <a class="nav-link" href="logs.php">
                    <i class="fas fa-history me-2"></i>سجل النشاط
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة الطلاب</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                    <i class="fas fa-download me-2"></i>تصدير البيانات
                </button>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                            <h4 class="fw-bold text-primary"><?php echo number_format($student_stats['total']); ?></h4>
                            <p class="text-muted mb-0">إجمالي الطلاب</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4 class="fw-bold text-warning"><?php echo number_format($student_stats['pending']); ?></h4>
                            <p class="text-muted mb-0">قيد المراجعة</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4 class="fw-bold text-success"><?php echo number_format($student_stats['approved']); ?></h4>
                            <p class="text-muted mb-0">مقبول</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                            <h4 class="fw-bold text-danger"><?php echo number_format($student_stats['rejected']); ?></h4>
                            <p class="text-muted mb-0">مرفوض</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">حالة الحساب</label>
                            <select class="form-select" id="status" name="status">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>مقبول</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="البحث بالاسم أو البريد أو رقم الطالب">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Students Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>قائمة الطلاب
                        <span class="badge bg-primary ms-2"><?php echo count($students); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($students)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد نتائج للبحث</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover" id="studentsTable">
                                <thead>
                                    <tr>
                                        <th>الطالب</th>
                                        <th>المعلومات الأكاديمية</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>النشاط</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($student['university_card_image']): ?>
                                                    <img src="../assets/uploads/<?php echo htmlspecialchars($student['university_card_image']); ?>" 
                                                         class="rounded-circle me-3" width="40" height="40" style="object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-primary rounded-circle me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($student['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($student['email']); ?></small>
                                                    <br>
                                                    <small class="text-muted">ID: <?php echo htmlspecialchars($student['student_id']); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($student['university_level']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo htmlspecialchars($student['specialization']); ?></small>
                                        </td>
                                        <td><?php echo format_date($student['created_at'], 'Y/m/d H:i'); ?></td>
                                        <td>
                                            <span class="badge status-<?php echo $student['account_status']; ?>">
                                                <?php 
                                                $status_text = [
                                                    'pending' => 'قيد المراجعة',
                                                    'approved' => 'مقبول',
                                                    'rejected' => 'مرفوض'
                                                ];
                                                echo $status_text[$student['account_status']];
                                                ?>
                                            </span>
                                            <?php if (!$student['is_active']): ?>
                                                <br><span class="badge bg-secondary mt-1">غير نشط</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($student['last_login']): ?>
                                                <small class="text-success">
                                                    <i class="fas fa-circle"></i>
                                                    <?php echo format_date($student['last_login'], 'Y/m/d'); ?>
                                                </small>
                                            <?php else: ?>
                                                <small class="text-muted">لم يسجل دخول</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="viewStudent(<?php echo $student['id']; ?>)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                
                                                <?php if ($student['account_status'] === 'pending'): ?>
                                                    <button class="btn btn-sm btn-outline-success" 
                                                            onclick="approveStudent(<?php echo $student['id']; ?>)">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="rejectStudent(<?php echo $student['id']; ?>)">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <?php if ($student['is_active']): ?>
                                                    <button class="btn btn-sm btn-outline-warning" 
                                                            onclick="deactivateStudent(<?php echo $student['id']; ?>)">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-sm btn-outline-success" 
                                                            onclick="activateStudent(<?php echo $student['id']; ?>)">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Student Details Modal -->
<div class="modal fade" id="studentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الطالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<!-- Rejection Reason Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" id="rejectForm">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="reject">
                <input type="hidden" name="student_id" id="rejectStudentId">
                
                <div class="modal-header">
                    <h5 class="modal-title">رفض الطالب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض *</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" 
                                  rows="3" required placeholder="اكتب سبب رفض الطالب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطالب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// عرض تفاصيل الطالب
function viewStudent(studentId) {
    fetch(`student-details.php?id=${studentId}`)
        .then(response => response.text())
        .then(data => {
            document.getElementById('studentDetails').innerHTML = data;
            new bootstrap.Modal(document.getElementById('studentModal')).show();
        })
        .catch(error => {
            showError('فشل في تحميل تفاصيل الطالب');
        });
}

// قبول الطالب
function approveStudent(studentId) {
    Swal.fire({
        title: 'تأكيد القبول',
        text: 'هل أنت متأكد من قبول هذا الطالب؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، اقبل',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            submitAction('approve', studentId);
        }
    });
}

// رفض الطالب
function rejectStudent(studentId) {
    document.getElementById('rejectStudentId').value = studentId;
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

// تفعيل الطالب
function activateStudent(studentId) {
    submitAction('activate', studentId);
}

// إلغاء تفعيل الطالب
function deactivateStudent(studentId) {
    Swal.fire({
        title: 'تأكيد إلغاء التفعيل',
        text: 'هل أنت متأكد من إلغاء تفعيل هذا الطالب؟',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ffc107',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'نعم، ألغ التفعيل',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            submitAction('deactivate', studentId);
        }
    });
}

// إرسال الإجراء
function submitAction(action, studentId) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
        <input type="hidden" name="action" value="${action}">
        <input type="hidden" name="student_id" value="${studentId}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// تهيئة جدول البيانات
document.addEventListener('DOMContentLoaded', function() {
    initializeDataTable('studentsTable');
});
</script>

<?php include '../includes/footer.php'; ?>
