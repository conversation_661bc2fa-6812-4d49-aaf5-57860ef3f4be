<?php
/**
 * اختبار إضافة دورة مباشرة
 * Test Direct Course Addition
 */

require_once 'config/config.php';

echo "<h1>اختبار إضافة دورة</h1>";

if (!$db) {
    echo "<div style='color: red;'>خطأ: لا يوجد اتصال بقاعدة البيانات</div>";
    exit;
}

$message = '';
$message_type = '';

// إضافة دورة تجريبية
if (isset($_POST['add_course'])) {
    try {
        $course_data = [
            'course_name' => 'دورة تجريبية - ' . date('Y-m-d H:i:s'),
            'course_description' => 'وصف الدورة التجريبية',
            'instructor_name' => 'مدرب تجريبي',
            'duration_hours' => 20,
            'price' => 99.99,
            'start_date' => date('Y-m-d'),
            'end_date' => date('Y-m-d', strtotime('+30 days')),
            'max_students' => 30,
            'course_link' => 'https://www.youtube.com/watch?v=test',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        echo "<h3>البيانات المرسلة:</h3>";
        echo "<pre>" . print_r($course_data, true) . "</pre>";
        
        $course_id = $database->insert('courses', $course_data);
        
        if ($course_id) {
            $message = "تم إضافة الدورة بنجاح! معرف الدورة: {$course_id}";
            $message_type = 'success';
        } else {
            $message = 'فشل في إضافة الدورة';
            $message_type = 'error';
        }
        
    } catch (Exception $e) {
        $message = 'خطأ: ' . $e->getMessage();
        $message_type = 'error';
    }
}

// عرض الدورات الموجودة
echo "<h2>الدورات الموجودة:</h2>";
try {
    $courses = $database->fetchAll("SELECT * FROM courses ORDER BY created_at DESC LIMIT 10");
    
    if (is_array($courses) && count($courses) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>اسم الدورة</th>";
        echo "<th style='padding: 8px;'>المدرب</th>";
        echo "<th style='padding: 8px;'>السعر</th>";
        echo "<th style='padding: 8px;'>الحالة</th>";
        echo "<th style='padding: 8px;'>تاريخ الإنشاء</th>";
        echo "</tr>";
        
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $course['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($course['course_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($course['instructor_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . $course['price'] . " USD</td>";
            echo "<td style='padding: 8px;'>" . ($course['is_active'] ? 'نشط' : 'غير نشط') . "</td>";
            echo "<td style='padding: 8px;'>" . $course['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>لا توجد دورات</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في جلب الدورات: " . $e->getMessage() . "</p>";
}

if ($message) {
    $color = $message_type === 'error' ? 'red' : 'green';
    echo "<div style='color: {$color}; padding: 10px; background: #f8f9fa; border-radius: 5px; margin: 20px 0;'>";
    echo $message;
    echo "</div>";
}

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات:</h2>";

try {
    // فحص جدول الدورات
    $table_info = $database->fetchAll("DESCRIBE courses");
    if ($table_info) {
        echo "<h3>بنية جدول الدورات:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($table_info as $field) {
            echo "<tr>";
            echo "<td>" . $field['Field'] . "</td>";
            echo "<td>" . $field['Type'] . "</td>";
            echo "<td>" . $field['Null'] . "</td>";
            echo "<td>" . $field['Key'] . "</td>";
            echo "<td>" . $field['Default'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في فحص الجدول: " . $e->getMessage() . "</p>";
    echo "<p><a href='create_tables.php'>إنشاء الجداول</a></p>";
}
?>

<form method="POST" style="margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 5px;">
    <h3>إضافة دورة تجريبية:</h3>
    <button type="submit" name="add_course" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        إضافة دورة تجريبية
    </button>
</form>

<div style="margin: 20px 0;">
    <h3>روابط الاختبار:</h3>
    <ul>
        <li><a href="admin/courses.php">صفحة إدارة الدورات</a></li>
        <li><a href="create_tables.php">إنشاء الجداول</a></li>
        <li><a href="test_final.php">اختبار شامل</a></li>
    </ul>
</div>

<script>
// تحديث الصفحة كل 30 ثانية لعرض أحدث البيانات
setTimeout(function() {
    location.reload();
}, 30000);
</script>
