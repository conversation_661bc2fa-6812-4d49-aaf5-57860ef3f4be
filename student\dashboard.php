<?php
/**
 * لوحة تحكم الطالب
 * Student Dashboard
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'لوحة تحكم الطالب';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على بيانات المستخدم
$user = $userManager->getUserById($user_id);

// الحصول على إحصائيات الطالب
$student_stats = [
    'enrolled_courses' => $database->fetchOne(
        "SELECT COUNT(*) as count FROM enrollments WHERE user_id = ?", 
        [$user_id]
    )['count'] ?? 0,
    
    'completed_courses' => $database->fetchOne(
        "SELECT COUNT(*) as count FROM enrollments WHERE user_id = ? AND enrollment_status = 'completed'", 
        [$user_id]
    )['count'] ?? 0,
    
    'pending_payments' => $database->fetchOne(
        "SELECT COUNT(*) as count FROM enrollments WHERE user_id = ? AND payment_status = 'pending'", 
        [$user_id]
    )['count'] ?? 0,
    
    'certificates' => $database->fetchOne(
        "SELECT COUNT(*) as count FROM enrollments WHERE user_id = ? AND certificate_issued = 1", 
        [$user_id]
    )['count'] ?? 0
];

// الحصول على أحدث الدورات المسجل بها
$recent_courses = $database->fetchAll(
    "SELECT c.*, e.enrollment_date, e.payment_status, e.enrollment_status 
     FROM courses c 
     JOIN enrollments e ON c.id = e.course_id 
     WHERE e.user_id = ? 
     ORDER BY e.enrollment_date DESC 
     LIMIT 5",
    [$user_id]
);

// الحصول على الإشعارات غير المقروءة
$notifications = $database->fetchAll(
    "SELECT * FROM notifications WHERE user_id = ? AND is_read = 0 ORDER BY created_at DESC LIMIT 5",
    [$user_id]
);

// الحصول على النشاط الأخير
$recent_activity = $database->fetchAll(
    "SELECT * FROM activity_logs WHERE user_id = ? ORDER BY created_at DESC LIMIT 10",
    [$user_id]
);

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <div class="mb-3">
                    <?php if ($user['university_card_image']): ?>
                        <img src="../assets/uploads/<?php echo htmlspecialchars($user['university_card_image']); ?>" 
                             class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                    <?php else: ?>
                        <i class="fas fa-user-circle fa-5x"></i>
                    <?php endif; ?>
                </div>
                <h5 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                <p class="mb-0 small"><?php echo htmlspecialchars($user['student_id']); ?></p>
                <span class="badge status-<?php echo $user['account_status']; ?> mt-2">
                    <?php 
                    $status_text = [
                        'pending' => 'قيد المراجعة',
                        'approved' => 'مفعل',
                        'rejected' => 'مرفوض'
                    ];
                    echo $status_text[$user['account_status']];
                    ?>
                </span>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link active" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                    <?php if (count($notifications) > 0): ?>
                        <span class="badge bg-danger ms-2"><?php echo count($notifications); ?></span>
                    <?php endif; ?>
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <!-- Welcome Section -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></h2>
                    <p class="text-muted mb-0">
                        آخر دخول: <?php echo $user['last_login'] ? format_date($user['last_login'], 'Y/m/d H:i') : 'لم يسجل دخول من قبل'; ?>
                    </p>
                </div>
                <div class="text-end">
                    <div id="current-time" class="text-muted small"></div>
                </div>
            </div>
            
            <!-- Account Status Alert -->
            <?php if ($user['account_status'] !== 'approved'): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php if ($user['account_status'] === 'pending'): ?>
                        حسابك قيد المراجعة. سيتم إشعارك عند الموافقة عليه.
                    <?php elseif ($user['account_status'] === 'rejected'): ?>
                        تم رفض حسابك. السبب: <?php echo htmlspecialchars($user['rejection_reason']); ?>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-book fa-2x text-primary mb-2"></i>
                            <h4 class="fw-bold text-primary"><?php echo $student_stats['enrolled_courses']; ?></h4>
                            <p class="text-muted mb-0">دورة مسجل بها</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h4 class="fw-bold text-success"><?php echo $student_stats['completed_courses']; ?></h4>
                            <p class="text-muted mb-0">دورة مكتملة</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4 class="fw-bold text-warning"><?php echo $student_stats['pending_payments']; ?></h4>
                            <p class="text-muted mb-0">دفعة معلقة</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card text-center border-0 shadow-sm">
                        <div class="card-body">
                            <i class="fas fa-certificate fa-2x text-info mb-2"></i>
                            <h4 class="fw-bold text-info"><?php echo $student_stats['certificates']; ?></h4>
                            <p class="text-muted mb-0">شهادة حاصل عليها</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Recent Courses -->
                <div class="col-lg-8 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-book me-2"></i>أحدث الدورات
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_courses)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لم تسجل في أي دورة بعد</p>
                                    <a href="browse-courses.php" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>تصفح الدورات
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم الدورة</th>
                                                <th>تاريخ التسجيل</th>
                                                <th>حالة الدفع</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_courses as $course): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($course['course_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($course['instructor_name']); ?></small>
                                                </td>
                                                <td><?php echo format_date($course['enrollment_date'], 'Y/m/d'); ?></td>
                                                <td>
                                                    <span class="badge status-<?php echo $course['payment_status']; ?>">
                                                        <?php 
                                                        $payment_status = [
                                                            'pending' => 'معلق',
                                                            'completed' => 'مكتمل',
                                                            'failed' => 'فاشل'
                                                        ];
                                                        echo $payment_status[$course['payment_status']];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge status-<?php echo $course['enrollment_status']; ?>">
                                                        <?php 
                                                        $enrollment_status = [
                                                            'enrolled' => 'مسجل',
                                                            'completed' => 'مكتمل',
                                                            'dropped' => 'منسحب'
                                                        ];
                                                        echo $enrollment_status[$course['enrollment_status']];
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="course-details.php?id=<?php echo $course['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center">
                                    <a href="courses.php" class="btn btn-outline-primary">
                                        <i class="fas fa-list me-2"></i>عرض جميع الدورات
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Notifications -->
                <div class="col-lg-4 mb-4">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bell me-2"></i>الإشعارات
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($notifications)): ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد إشعارات جديدة</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item mb-3 p-3 bg-light rounded">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($notification['title']); ?></h6>
                                    <p class="mb-1 small"><?php echo htmlspecialchars($notification['message']); ?></p>
                                    <small class="text-muted">
                                        <?php echo format_date($notification['created_at'], 'Y/m/d H:i'); ?>
                                    </small>
                                </div>
                                <?php endforeach; ?>
                                <div class="text-center">
                                    <a href="notifications.php" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-list me-2"></i>عرض الكل
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-white">
                            <h5 class="mb-0">
                                <i class="fas fa-history me-2"></i>النشاط الأخير
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_activity)): ?>
                                <p class="text-muted text-center">لا يوجد نشاط مسجل</p>
                            <?php else: ?>
                                <div class="timeline">
                                    <?php foreach ($recent_activity as $activity): ?>
                                    <div class="timeline-item mb-3">
                                        <div class="d-flex">
                                            <div class="timeline-marker me-3">
                                                <i class="fas fa-circle text-primary"></i>
                                            </div>
                                            <div class="timeline-content">
                                                <p class="mb-1"><?php echo htmlspecialchars($activity['activity_description']); ?></p>
                                                <small class="text-muted">
                                                    <?php echo format_date($activity['created_at'], 'Y/m/d H:i'); ?>
                                                    - IP: <?php echo htmlspecialchars($activity['ip_address']); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-marker {
    width: 20px;
    text-align: center;
}

.timeline-content {
    flex: 1;
}

.notification-item {
    border-left: 4px solid #007bff;
}
</style>

<?php include '../includes/footer.php'; ?>
