<?php
/**
 * الدوال المساعدة للنظام
 * System Helper Functions
 */

require_once __DIR__ . '/../config/config.php';

/**
 * كلاس إدارة المستخدمين
 */
class UserManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * تسجيل مستخدم جديد
     */
    public function register($data) {
        // التحقق من وجود البريد الإلكتروني
        $existing_user = $this->db->fetchOne(
            "SELECT id FROM users WHERE email = ? OR student_id = ?", 
            [$data['email'], $data['student_id']]
        );
        
        if ($existing_user) {
            return ['success' => false, 'message' => 'البريد الإلكتروني أو رقم الطالب موجود مسبقاً'];
        }
        
        // تشفير كلمة المرور
        $data['password'] = password_hash($data['password'], HASH_ALGO);
        
        // إدراج المستخدم
        $user_id = $this->db->insert('users', $data);
        
        if ($user_id) {
            log_activity($user_id, 'register', 'تسجيل حساب جديد');
            return ['success' => true, 'user_id' => $user_id, 'message' => 'تم التسجيل بنجاح'];
        }
        
        return ['success' => false, 'message' => 'فشل في التسجيل'];
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($email, $password) {
        $user = $this->db->fetchOne(
            "SELECT * FROM users WHERE email = ? AND is_active = 1", 
            [$email]
        );
        
        if (!$user) {
            log_activity(null, 'login_failed', 'محاولة دخول بإيميل غير موجود: ' . $email);
            return ['success' => false, 'message' => 'بيانات الدخول غير صحيحة'];
        }
        
        if (!password_verify($password, $user['password'])) {
            log_activity($user['id'], 'login_failed', 'محاولة دخول بكلمة مرور خاطئة');
            return ['success' => false, 'message' => 'بيانات الدخول غير صحيحة'];
        }
        
        if ($user['account_status'] !== 'approved') {
            $status_msg = [
                'pending' => 'حسابك قيد المراجعة',
                'rejected' => 'تم رفض حسابك: ' . $user['rejection_reason']
            ];
            return ['success' => false, 'message' => $status_msg[$user['account_status']]];
        }
        
        // تحديث آخر دخول
        $this->db->update('users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
        
        // إنشاء الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['email'] = $user['email'];
        $_SESSION['last_activity'] = time();
        
        log_activity($user['id'], 'login', 'تسجيل دخول ناجح');
        
        return ['success' => true, 'user' => $user];
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        if (is_logged_in()) {
            log_activity($_SESSION['user_id'], 'logout', 'تسجيل خروج');
        }
        
        session_destroy();
        return true;
    }
    
    /**
     * الحصول على بيانات المستخدم
     */
    public function getUserById($user_id) {
        return $this->db->fetchOne("SELECT * FROM users WHERE id = ?", [$user_id]);
    }
    
    /**
     * تحديث حالة الحساب
     */
    public function updateAccountStatus($user_id, $status, $reason = null) {
        $data = ['account_status' => $status];
        if ($reason) {
            $data['rejection_reason'] = $reason;
        }
        
        $result = $this->db->update('users', $data, 'id = ?', [$user_id]);
        
        if ($result) {
            log_activity($_SESSION['user_id'], 'account_status_update', 
                "تغيير حالة الحساب للمستخدم {$user_id} إلى {$status}");
            
            // إرسال إشعار للمستخدم
            $this->sendNotification($user_id, 'تحديث حالة الحساب', 
                "تم تحديث حالة حسابك إلى: {$status}" . ($reason ? " - السبب: {$reason}" : ""));
        }
        
        return $result;
    }
    
    /**
     * إرسال إشعار للمستخدم
     */
    public function sendNotification($user_id, $title, $message, $type = 'info') {
        $data = [
            'user_id' => $user_id,
            'title' => $title,
            'message' => $message,
            'notification_type' => $type
        ];
        
        return $this->db->insert('notifications', $data);
    }
}

/**
 * كلاس إدارة الدورات
 */
class CourseManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * الحصول على جميع الدورات النشطة
     */
    public function getActiveCourses() {
        return $this->db->fetchAll(
            "SELECT * FROM courses WHERE is_active = 1 ORDER BY created_at DESC"
        );
    }
    
    /**
     * الحصول على دورة بالمعرف
     */
    public function getCourseById($course_id) {
        return $this->db->fetchOne("SELECT * FROM courses WHERE id = ?", [$course_id]);
    }
    
    /**
     * إضافة دورة جديدة
     */
    public function addCourse($data) {
        $course_id = $this->db->insert('courses', $data);
        
        if ($course_id) {
            log_activity($_SESSION['user_id'], 'course_add', "إضافة دورة جديدة: {$data['course_name']}");
        }
        
        return $course_id;
    }
    
    /**
     * تحديث دورة
     */
    public function updateCourse($course_id, $data) {
        $result = $this->db->update('courses', $data, 'id = ?', [$course_id]);
        
        if ($result) {
            log_activity($_SESSION['user_id'], 'course_update', "تحديث الدورة رقم {$course_id}");
        }
        
        return $result;
    }
    
    /**
     * التسجيل في دورة
     */
    public function enrollInCourse($user_id, $course_id) {
        // التحقق من عدم التسجيل المسبق
        $existing = $this->db->fetchOne(
            "SELECT id FROM enrollments WHERE user_id = ? AND course_id = ?",
            [$user_id, $course_id]
        );
        
        if ($existing) {
            return ['success' => false, 'message' => 'أنت مسجل في هذه الدورة مسبقاً'];
        }
        
        // التحقق من توفر مقاعد
        $course = $this->getCourseById($course_id);
        if ($course['current_enrolled'] >= $course['max_students']) {
            return ['success' => false, 'message' => 'الدورة مكتملة العدد'];
        }
        
        $enrollment_data = [
            'user_id' => $user_id,
            'course_id' => $course_id,
            'payment_status' => 'pending'
        ];
        
        $enrollment_id = $this->db->insert('enrollments', $enrollment_data);
        
        if ($enrollment_id) {
            log_activity($user_id, 'course_enroll', "التسجيل في الدورة رقم {$course_id}");
            return ['success' => true, 'enrollment_id' => $enrollment_id];
        }
        
        return ['success' => false, 'message' => 'فشل في التسجيل'];
    }
    
    /**
     * الحصول على دورات المستخدم
     */
    public function getUserCourses($user_id) {
        return $this->db->fetchAll(
            "SELECT c.*, e.enrollment_date, e.payment_status, e.enrollment_status 
             FROM courses c 
             JOIN enrollments e ON c.id = e.course_id 
             WHERE e.user_id = ? 
             ORDER BY e.enrollment_date DESC",
            [$user_id]
        );
    }
}

/**
 * كلاس إدارة المدفوعات
 */
class PaymentManager {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * إنشاء دفعة جديدة
     */
    public function createPayment($user_id, $course_id, $enrollment_id, $amount) {
        $payment_data = [
            'user_id' => $user_id,
            'course_id' => $course_id,
            'enrollment_id' => $enrollment_id,
            'amount' => $amount,
            'payment_method' => 'paypal',
            'payment_gateway' => 'paypal',
            'payment_status' => 'pending'
        ];
        
        return $this->db->insert('payments', $payment_data);
    }
    
    /**
     * تحديث حالة الدفع
     */
    public function updatePaymentStatus($payment_id, $status, $transaction_id = null, $response = null) {
        $data = ['payment_status' => $status];
        
        if ($transaction_id) {
            $data['transaction_id'] = $transaction_id;
        }
        
        if ($response) {
            $data['gateway_response'] = $response;
        }
        
        $result = $this->db->update('payments', $data, 'id = ?', [$payment_id]);
        
        if ($result && $status === 'completed') {
            // تحديث حالة التسجيل
            $payment = $this->db->fetchOne("SELECT * FROM payments WHERE id = ?", [$payment_id]);
            $this->db->update('enrollments', 
                ['payment_status' => 'completed'], 
                'id = ?', 
                [$payment['enrollment_id']]
            );
            
            // زيادة عدد المسجلين
            $this->db->executeQuery(
                "UPDATE courses SET current_enrolled = current_enrolled + 1 WHERE id = ?",
                [$payment['course_id']]
            );
            
            log_activity($payment['user_id'], 'payment_completed', 
                "اكتمال دفع الدورة رقم {$payment['course_id']}");
        }
        
        return $result;
    }
}

// إنشاء كائنات الكلاسات
$userManager = new UserManager($database);
$courseManager = new CourseManager($database);
$paymentManager = new PaymentManager($database);
?>
