<?php
/**
 * صفحة دورات الطالب
 * Student Courses Page
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'دوراتي';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على دورات الطالب
$user_courses = $courseManager->getUserCourses($user_id);

// تصنيف الدورات حسب الحالة
$courses_by_status = [
    'pending' => [],
    'active' => [],
    'completed' => []
];

foreach ($user_courses as $course) {
    if ($course['payment_status'] === 'pending') {
        $courses_by_status['pending'][] = $course;
    } elseif ($course['enrollment_status'] === 'completed') {
        $courses_by_status['completed'][] = $course;
    } else {
        $courses_by_status['active'][] = $course;
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <i class="fas fa-book fa-3x mb-3"></i>
                <h5>دوراتي</h5>
                <p class="small mb-0">إدارة الدورات المسجل بها</p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link active" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>دوراتي</h2>
                <a href="browse-courses.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>تصفح دورات جديدة
                </a>
            </div>
            
            <?php if (empty($user_courses)): ?>
                <!-- Empty State -->
                <div class="text-center py-5">
                    <i class="fas fa-book fa-5x text-muted mb-4"></i>
                    <h3 class="text-muted mb-3">لم تسجل في أي دورة بعد</h3>
                    <p class="text-muted mb-4">ابدأ رحلتك التعليمية واكتسب مهارات جديدة</p>
                    <a href="browse-courses.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>تصفح الدورات المتاحة
                    </a>
                </div>
            <?php else: ?>
                
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs mb-4" id="courseTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button">
                            <i class="fas fa-play-circle me-2"></i>الدورات النشطة
                            <span class="badge bg-primary ms-2"><?php echo count($courses_by_status['active']); ?></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button">
                            <i class="fas fa-clock me-2"></i>في انتظار الدفع
                            <span class="badge bg-warning ms-2"><?php echo count($courses_by_status['pending']); ?></span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button">
                            <i class="fas fa-check-circle me-2"></i>الدورات المكتملة
                            <span class="badge bg-success ms-2"><?php echo count($courses_by_status['completed']); ?></span>
                        </button>
                    </li>
                </ul>
                
                <!-- Tabs Content -->
                <div class="tab-content" id="courseTabsContent">
                    
                    <!-- Active Courses -->
                    <div class="tab-pane fade show active" id="active" role="tabpanel">
                        <?php if (empty($courses_by_status['active'])): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-play-circle fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد دورات نشطة حالياً</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($courses_by_status['active'] as $course): ?>
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="card course-card border-0 shadow-sm h-100">
                                        <?php if ($course['course_image']): ?>
                                            <img src="../assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                                                 class="card-img-top course-image" alt="<?php echo htmlspecialchars($course['course_name']); ?>">
                                        <?php else: ?>
                                            <div class="card-img-top course-image bg-primary d-flex align-items-center justify-content-center">
                                                <i class="fas fa-book fa-3x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body d-flex flex-column">
                                            <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($course['instructor_name']); ?>
                                            </p>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $course['duration_hours']; ?> ساعة
                                            </p>
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ التسجيل: <?php echo format_date($course['enrollment_date'], 'Y/m/d'); ?>
                                            </p>
                                            
                                            <div class="mt-auto">
                                                <?php if ($course['is_link_active'] && $course['course_link']): ?>
                                                    <a href="<?php echo htmlspecialchars($course['course_link']); ?>" 
                                                       target="_blank" class="btn btn-success w-100 mb-2">
                                                        <i class="fas fa-external-link-alt me-2"></i>دخول الدورة
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-secondary w-100 mb-2" disabled>
                                                        <i class="fas fa-lock me-2"></i>الدورة غير متاحة بعد
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <a href="course-details.php?id=<?php echo $course['id']; ?>" 
                                                   class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-info-circle me-2"></i>تفاصيل الدورة
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Pending Payment Courses -->
                    <div class="tab-pane fade" id="pending" role="tabpanel">
                        <?php if (empty($courses_by_status['pending'])): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد دورات في انتظار الدفع</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($courses_by_status['pending'] as $course): ?>
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="card course-card border-warning shadow-sm h-100">
                                        <?php if ($course['course_image']): ?>
                                            <img src="../assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                                                 class="card-img-top course-image" alt="<?php echo htmlspecialchars($course['course_name']); ?>">
                                        <?php else: ?>
                                            <div class="card-img-top course-image bg-warning d-flex align-items-center justify-content-center">
                                                <i class="fas fa-book fa-3x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body d-flex flex-column">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                                <span class="badge bg-warning">معلق</span>
                                            </div>
                                            
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($course['instructor_name']); ?>
                                            </p>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-dollar-sign me-1"></i>
                                                <?php echo format_currency($course['price']); ?>
                                            </p>
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ التسجيل: <?php echo format_date($course['enrollment_date'], 'Y/m/d'); ?>
                                            </p>
                                            
                                            <div class="mt-auto">
                                                <a href="payment.php?course_id=<?php echo $course['id']; ?>" 
                                                   class="btn btn-warning w-100 mb-2">
                                                    <i class="fas fa-credit-card me-2"></i>إكمال الدفع
                                                </a>
                                                
                                                <a href="course-details.php?id=<?php echo $course['id']; ?>" 
                                                   class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-info-circle me-2"></i>تفاصيل الدورة
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Completed Courses -->
                    <div class="tab-pane fade" id="completed" role="tabpanel">
                        <?php if (empty($courses_by_status['completed'])): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لم تكمل أي دورة بعد</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($courses_by_status['completed'] as $course): ?>
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="card course-card border-success shadow-sm h-100">
                                        <?php if ($course['course_image']): ?>
                                            <img src="../assets/uploads/<?php echo htmlspecialchars($course['course_image']); ?>" 
                                                 class="card-img-top course-image" alt="<?php echo htmlspecialchars($course['course_name']); ?>">
                                        <?php else: ?>
                                            <div class="card-img-top course-image bg-success d-flex align-items-center justify-content-center">
                                                <i class="fas fa-book fa-3x text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="card-body d-flex flex-column">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h5 class="card-title fw-bold"><?php echo htmlspecialchars($course['course_name']); ?></h5>
                                                <span class="badge bg-success">مكتمل</span>
                                            </div>
                                            
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($course['instructor_name']); ?>
                                            </p>
                                            <p class="text-muted mb-2">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $course['duration_hours']; ?> ساعة
                                            </p>
                                            <p class="text-muted mb-3">
                                                <i class="fas fa-calendar-check me-1"></i>
                                                تاريخ الإكمال: <?php echo $course['completion_date'] ? format_date($course['completion_date'], 'Y/m/d') : 'غير محدد'; ?>
                                            </p>
                                            
                                            <div class="mt-auto">
                                                <?php
                                                // التحقق من وجود شهادة
                                                $certificate = $database->fetchOne(
                                                    "SELECT * FROM enrollments WHERE user_id = ? AND course_id = ? AND certificate_issued = 1",
                                                    [$user_id, $course['id']]
                                                );
                                                ?>
                                                
                                                <?php if ($certificate): ?>
                                                    <a href="certificate.php?course_id=<?php echo $course['id']; ?>" 
                                                       class="btn btn-success w-100 mb-2">
                                                        <i class="fas fa-certificate me-2"></i>تحميل الشهادة
                                                    </a>
                                                <?php else: ?>
                                                    <button class="btn btn-outline-secondary w-100 mb-2" disabled>
                                                        <i class="fas fa-hourglass-half me-2"></i>الشهادة قيد الإعداد
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <a href="course-details.php?id=<?php echo $course['id']; ?>" 
                                                   class="btn btn-outline-primary w-100">
                                                    <i class="fas fa-info-circle me-2"></i>تفاصيل الدورة
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.course-image {
    height: 200px;
    object-fit: cover;
}

.course-card {
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.nav-tabs .nav-link:hover {
    border: none;
    color: #667eea;
}
</style>

<?php include '../includes/footer.php'; ?>
