<?php
/**
 * صفحة تشخيص المشاكل
 * Debug Page
 */

echo "<h1>تشخيص مشاكل النظام</h1>";

// 1. فحص ملف الإعدادات
echo "<h2>1. فحص ملف الإعدادات</h2>";

if (file_exists('config/database.php')) {
    echo "✓ ملف config/database.php موجود<br>";
    
    // قراءة محتوى الملف
    $config_content = file_get_contents('config/database.php');
    echo "✓ حجم الملف: " . strlen($config_content) . " بايت<br>";
    
    // فحص الثوابت
    if (strpos($config_content, "define('DB_HOST'") !== false) {
        echo "✓ DB_HOST موجود في الملف<br>";
    } else {
        echo "✗ DB_HOST غير موجود في الملف<br>";
    }
    
    if (strpos($config_content, "define('DB_NAME'") !== false) {
        echo "✓ DB_NAME موجود في الملف<br>";
    } else {
        echo "✗ DB_NAME غير موجود في الملف<br>";
    }
    
} else {
    echo "✗ ملف config/database.php غير موجود<br>";
}

echo "<hr>";

// 2. محاولة تضمين الملف
echo "<h2>2. تضمين ملف الإعدادات</h2>";

try {
    require_once 'config/database.php';
    echo "✓ تم تضمين الملف بنجاح<br>";
    
    // فحص الثوابت
    if (defined('DB_HOST')) {
        echo "✓ DB_HOST = " . DB_HOST . "<br>";
    } else {
        echo "✗ DB_HOST غير معرف<br>";
    }
    
    if (defined('DB_NAME')) {
        echo "✓ DB_NAME = " . DB_NAME . "<br>";
    } else {
        echo "✗ DB_NAME غير معرف<br>";
    }
    
    if (defined('DB_USER')) {
        echo "✓ DB_USER = " . DB_USER . "<br>";
    } else {
        echo "✗ DB_USER غير معرف<br>";
    }
    
    // فحص كائن قاعدة البيانات
    if (isset($database)) {
        echo "✓ كائن \$database موجود<br>";
        echo "✓ نوع الكائن: " . get_class($database) . "<br>";
    } else {
        echo "✗ كائن \$database غير موجود<br>";
    }
    
    if (isset($db)) {
        if ($db === null) {
            echo "✗ \$db = null (فشل الاتصال)<br>";
        } else {
            echo "✓ \$db موجود ونشط<br>";
            echo "✓ نوع الكائن: " . get_class($db) . "<br>";
        }
    } else {
        echo "✗ \$db غير موجود<br>";
    }
    
} catch (Exception $e) {
    echo "✗ خطأ في تضمين الملف: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// 3. اختبار الاتصال المباشر
echo "<h2>3. اختبار الاتصال المباشر</h2>";

try {
    $test_pdo = new PDO("mysql:host=localhost", "root", "");
    echo "✓ الاتصال بخادم MySQL نجح<br>";
    
    // فحص قواعد البيانات
    $databases = $test_pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    echo "✓ قواعد البيانات الموجودة: " . implode(', ', $databases) . "<br>";
    
    if (in_array('elzam_training_system', $databases)) {
        echo "✓ قاعدة بيانات elzam_training_system موجودة<br>";
        
        // الاتصال بقاعدة البيانات
        $test_pdo = new PDO("mysql:host=localhost;dbname=elzam_training_system", "root", "");
        echo "✓ الاتصال بقاعدة البيانات نجح<br>";
        
        // فحص الجداول
        $tables = $test_pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        if (!empty($tables)) {
            echo "✓ الجداول الموجودة: " . implode(', ', $tables) . "<br>";
        } else {
            echo "⚠ قاعدة البيانات فارغة<br>";
        }
        
    } else {
        echo "✗ قاعدة بيانات elzam_training_system غير موجودة<br>";
    }
    
} catch (Exception $e) {
    echo "✗ فشل الاتصال: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// 4. فحص امتدادات PHP
echo "<h2>4. امتدادات PHP</h2>";

$extensions = ['pdo', 'pdo_mysql', 'mysqli'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ {$ext} متوفر<br>";
    } else {
        echo "✗ {$ext} غير متوفر<br>";
    }
}

echo "<hr>";

// 5. معلومات PHP
echo "<h2>5. معلومات PHP</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";

echo "<hr>";

// 6. الحلول المقترحة
echo "<h2>6. الحلول المقترحة</h2>";
echo "<ul>";
echo "<li><a href='install_simple.php'>تشغيل معالج التثبيت المبسط</a></li>";
echo "<li><a href='setup_database.php'>إعداد قاعدة البيانات السريع</a></li>";
echo "<li><a href='check_system.php'>فحص شامل للنظام</a></li>";
echo "<li><a href='index.php'>العودة للصفحة الرئيسية</a></li>";
echo "</ul>";

// 7. إنشاء قاعدة البيانات تلقائياً
echo "<hr>";
echo "<h2>7. إنشاء قاعدة البيانات تلقائياً</h2>";

if (isset($_GET['create_db'])) {
    try {
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->exec("CREATE DATABASE IF NOT EXISTS elzam_training_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<div style='color: green; font-weight: bold;'>✓ تم إنشاء قاعدة البيانات بنجاح!</div>";
        echo "<a href='install_simple.php'>اذهب لإكمال التثبيت</a>";
    } catch (Exception $e) {
        echo "<div style='color: red; font-weight: bold;'>✗ فشل إنشاء قاعدة البيانات: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<a href='?create_db=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء قاعدة البيانات الآن</a>";
}
?>
