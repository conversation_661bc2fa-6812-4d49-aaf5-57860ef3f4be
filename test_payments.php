<?php
/**
 * اختبار صفحة المدفوعات
 * Test Payments Page
 */

session_start();

echo "<h1>اختبار صفحة المدفوعات</h1>";

// التحقق من الجلسة
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    echo "<div style='color: red; padding: 10px; background: #f8d7da; border-radius: 5px;'>";
    echo "يجب تسجيل الدخول كمدير أولاً. <a href='simple_login.php'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

// تضمين الملفات المطلوبة
require_once 'config/config.php';

echo "<h2>1. فحص الجلسة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>معرف المستخدم:</strong> " . $_SESSION['user_id'] . "<br>";
echo "<strong>نوع المستخدم:</strong> " . ($_SESSION['user_type'] ?? 'غير محدد') . "<br>";
echo "<strong>اسم المستخدم:</strong> " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>";
echo "</div>";

// فحص قاعدة البيانات
echo "<h2>2. فحص قاعدة البيانات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

try {
    if ($database) {
        echo "<span style='color: green;'>✓ كائن قاعدة البيانات متاح</span><br>";
    } else {
        echo "<span style='color: red;'>✗ كائن قاعدة البيانات غير متاح</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span><br>";
}

echo "</div>";

// فحص الجداول المطلوبة
echo "<h2>3. فحص الجداول المطلوبة:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

$required_tables = ['payments', 'users', 'courses', 'enrollments'];

foreach ($required_tables as $table) {
    try {
        $result = $database->fetchOne("SELECT COUNT(*) as count FROM {$table}");
        $count = $result['count'] ?? 0;
        echo "<span style='color: green;'>✓ جدول {$table}: {$count} سجل</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ جدول {$table}: غير موجود أو خطأ</span><br>";
    }
}

echo "</div>";

// اختبار استعلام المدفوعات
echo "<h2>4. اختبار استعلام المدفوعات:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

try {
    $payments = $database->fetchAll("
        SELECT p.*, 
               u.full_name, u.email, u.student_id,
               c.course_name, c.instructor_name,
               e.enrollment_date
        FROM payments p
        JOIN users u ON p.user_id = u.id
        JOIN courses c ON p.course_id = c.id
        JOIN enrollments e ON p.enrollment_id = e.id
        ORDER BY p.payment_date DESC
    ");
    
    if (is_array($payments)) {
        $count = count($payments);
        echo "<span style='color: green;'>✓ تم جلب المدفوعات بنجاح: {$count} مدفوعة</span><br>";
        
        if ($count > 0) {
            echo "<strong>أول مدفوعة:</strong><br>";
            $first = $payments[0];
            echo "- المعرف: " . $first['id'] . "<br>";
            echo "- الطالب: " . htmlspecialchars($first['full_name']) . "<br>";
            echo "- الدورة: " . htmlspecialchars($first['course_name']) . "<br>";
            echo "- المبلغ: " . $first['amount'] . "<br>";
            echo "- الحالة: " . $first['payment_status'] . "<br>";
        }
    } else {
        echo "<span style='color: orange;'>⚠ لا توجد مدفوعات أو خطأ في الاستعلام</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ خطأ في جلب المدفوعات: " . $e->getMessage() . "</span><br>";
    
    // اختبار الجداول منفردة
    echo "<br><strong>اختبار الجداول منفردة:</strong><br>";
    
    try {
        $payments_simple = $database->fetchAll("SELECT * FROM payments LIMIT 5");
        echo "<span style='color: green;'>✓ جدول payments: " . (is_array($payments_simple) ? count($payments_simple) : 0) . " سجل</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ جدول payments: " . $e->getMessage() . "</span><br>";
    }
    
    try {
        $users_simple = $database->fetchAll("SELECT id, full_name, email FROM users LIMIT 5");
        echo "<span style='color: green;'>✓ جدول users: " . (is_array($users_simple) ? count($users_simple) : 0) . " سجل</span><br>";
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ جدول users: " . $e->getMessage() . "</span><br>";
    }
}

echo "</div>";

// إنشاء بيانات تجريبية
echo "<h2>5. إنشاء بيانات تجريبية:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if (isset($_POST['create_sample_data'])) {
    try {
        // إنشاء جدول المدفوعات إذا لم يكن موجود
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                enrollment_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                payment_status VARCHAR(20) DEFAULT 'pending',
                transaction_id VARCHAR(100),
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        echo "<span style='color: green;'>✓ تم إنشاء جدول المدفوعات</span><br>";
        
        // إنشاء جدول الدورات إذا لم يكن موجود
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS courses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                course_name VARCHAR(255) NOT NULL,
                instructor_name VARCHAR(255) NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                is_active TINYINT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ");
        
        echo "<span style='color: green;'>✓ تم إنشاء جدول الدورات</span><br>";
        
        // إنشاء جدول التسجيلات إذا لم يكن موجود
        $database->executeQuery("
            CREATE TABLE IF NOT EXISTS enrollments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                enrollment_status VARCHAR(20) DEFAULT 'enrolled',
                payment_status VARCHAR(20) DEFAULT 'pending'
            )
        ");
        
        echo "<span style='color: green;'>✓ تم إنشاء جدول التسجيلات</span><br>";
        
        // إدراج دورة تجريبية
        $course_id = $database->insert('courses', [
            'course_name' => 'دورة تجريبية في البرمجة',
            'instructor_name' => 'أ. محمد أحمد',
            'price' => 299.99
        ]);
        
        if ($course_id) {
            echo "<span style='color: green;'>✓ تم إنشاء دورة تجريبية (ID: {$course_id})</span><br>";
            
            // إدراج تسجيل تجريبي
            $enrollment_id = $database->insert('enrollments', [
                'user_id' => $_SESSION['user_id'],
                'course_id' => $course_id
            ]);
            
            if ($enrollment_id) {
                echo "<span style='color: green;'>✓ تم إنشاء تسجيل تجريبي (ID: {$enrollment_id})</span><br>";
                
                // إدراج مدفوعة تجريبية
                $payment_id = $database->insert('payments', [
                    'user_id' => $_SESSION['user_id'],
                    'course_id' => $course_id,
                    'enrollment_id' => $enrollment_id,
                    'amount' => 299.99,
                    'payment_method' => 'paypal',
                    'payment_status' => 'completed',
                    'transaction_id' => 'TXN_' . time()
                ]);
                
                if ($payment_id) {
                    echo "<span style='color: green;'>✓ تم إنشاء مدفوعة تجريبية (ID: {$payment_id})</span><br>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ خطأ في إنشاء البيانات: " . $e->getMessage() . "</span><br>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='create_sample_data' style='background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 5px;'>إنشاء بيانات تجريبية</button>";
echo "</form>";

echo "</div>";

// روابط الاختبار
echo "<h2>6. اختبار الوصول:</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

echo "<a href='admin/payments.php' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المدفوعات الأصلية</a>";
echo "<a href='admin/index.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة الإدارة</a>";
echo "<a href='test_session.php' style='background: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار الجلسة</a>";

echo "</div>";

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='admin/payments.php'>صفحة المدفوعات</a></li>";
echo "<li><a href='admin/index.php'>لوحة الإدارة</a></li>";
echo "<li><a href='test_session.php'>اختبار الجلسة</a></li>";
echo "<li><a href='simple_login.php'>تسجيل دخول بسيط</a></li>";
echo "</ul>";
?>
