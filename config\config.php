<?php
/**
 * الإعدادات العامة للنظام
 * General System Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';

// إعدادات الموقع
define('SITE_NAME', 'نظام إدارة الدورات التدريبية - الزام');
define('SITE_URL', 'http://localhost/elzam-web-site');
define('SITE_EMAIL', '<EMAIL>');

// مسارات المجلدات
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/assets/uploads/');
define('IMAGES_PATH', ROOT_PATH . '/assets/images/');
define('CSS_PATH', '/assets/css/');
define('JS_PATH', '/assets/js/');

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOC_TYPES', ['pdf', 'doc', 'docx']);

// إعدادات الأمان
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_MIN_LENGTH', 8);
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_ENCRYPTION', 'tls');

// إعدادات الدفع
define('PAYPAL_MODE', 'sandbox'); // sandbox أو live
define('PAYPAL_CLIENT_ID', 'your-paypal-client-id');
define('PAYPAL_CLIENT_SECRET', 'your-paypal-client-secret');

// المناطق الزمنية
date_default_timezone_set('Asia/Riyadh');

/**
 * دالة تنظيف البيانات
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * دالة التحقق من صحة البريد الإلكتروني
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * دالة إنشاء رمز CSRF
 */
function generate_csrf_token() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * دالة التحقق من رمز CSRF
 */
function verify_csrf_token($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * دالة تسجيل النشاط
 */
function log_activity($user_id, $activity_type, $description, $page = null) {
    global $database;
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $session_id = session_id();
    $page = $page ?? $_SERVER['REQUEST_URI'] ?? 'unknown';
    
    $data = [
        'user_id' => $user_id,
        'ip_address' => $ip_address,
        'user_agent' => $user_agent,
        'activity_type' => $activity_type,
        'activity_description' => $description,
        'page_accessed' => $page,
        'session_id' => $session_id
    ];
    
    return $database->insert('activity_logs', $data);
}

/**
 * دالة التحقق من تسجيل الدخول
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

/**
 * دالة التحقق من صلاحيات المدير
 */
function is_admin() {
    return is_logged_in() && $_SESSION['user_type'] === 'admin';
}

/**
 * دالة التحقق من صلاحيات الطالب
 */
function is_student() {
    return is_logged_in() && $_SESSION['user_type'] === 'student';
}

/**
 * دالة إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * دالة عرض الرسائل
 */
function show_message($message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
}

/**
 * دالة الحصول على الرسائل
 */
function get_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * دالة رفع الملفات
 */
function upload_file($file, $upload_dir, $allowed_types = null) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $allowed_types = $allowed_types ?? ALLOWED_IMAGE_TYPES;
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $filename = uniqid() . '_' . time() . '.' . $file_extension;
    $upload_path = $upload_dir . $filename;
    
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $filename, 'path' => $upload_path];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

/**
 * دالة تنسيق التاريخ
 */
function format_date($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

/**
 * دالة تنسيق المبلغ
 */
function format_currency($amount, $currency = 'USD') {
    return number_format($amount, 2) . ' ' . $currency;
}

// التحقق من انتهاء الجلسة
if (is_logged_in() && isset($_SESSION['last_activity'])) {
    if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
        session_destroy();
        redirect('/auth/login.php?timeout=1');
    }
}

// تحديث وقت آخر نشاط
if (is_logged_in()) {
    $_SESSION['last_activity'] = time();
}
?>
