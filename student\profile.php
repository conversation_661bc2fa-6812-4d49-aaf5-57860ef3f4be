<?php
/**
 * الملف الشخصي للطالب
 * Student Profile
 */

require_once '../config/config.php';
require_once '../includes/functions.php';

$page_title = 'الملف الشخصي';

// التحقق من تسجيل الدخول والصلاحيات
if (!is_logged_in() || !is_student()) {
    redirect(SITE_URL . '/auth/login.php');
}

$user_id = $_SESSION['user_id'];
$message = '';
$message_type = '';

// الحصول على بيانات المستخدم
try {
    $user = $database->fetchOne("SELECT * FROM users WHERE id = ? LIMIT 1", [$user_id]);
    if (!$user) {
        redirect(SITE_URL . '/auth/login.php');
    }
} catch (Exception $e) {
    redirect(SITE_URL . '/auth/login.php');
}

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'رمز الأمان غير صحيح';
        $message_type = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            $full_name = sanitize_input($_POST['full_name'] ?? '');
            $email = sanitize_input($_POST['email'] ?? '');
            $university_level = sanitize_input($_POST['university_level'] ?? '');
            $specialization = sanitize_input($_POST['specialization'] ?? '');
            
            $errors = [];
            
            if (empty($full_name)) {
                $errors[] = 'الاسم الكامل مطلوب';
            }
            
            if (empty($email) || !validate_email($email)) {
                $errors[] = 'البريد الإلكتروني غير صحيح';
            }
            
            // التحقق من عدم وجود البريد الإلكتروني لمستخدم آخر
            $existing_user = $database->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
            if ($existing_user) {
                $errors[] = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
            }
            
            if (empty($errors)) {
                try {
                    $update_data = [
                        'full_name' => $full_name,
                        'email' => $email,
                        'university_level' => $university_level,
                        'specialization' => $specialization
                    ];
                    
                    $updated = $database->update('users', $update_data, 'id = ?', [$user_id]);
                    
                    if ($updated) {
                        // تحديث الجلسة
                        $_SESSION['user_name'] = $full_name;
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['user_email'] = $email;
                        
                        $message = 'تم تحديث البيانات بنجاح';
                        $message_type = 'success';
                        
                        // إعادة جلب البيانات المحدثة
                        $user = $database->fetchOne("SELECT * FROM users WHERE id = ? LIMIT 1", [$user_id]);
                    } else {
                        $message = 'لم يتم تحديث أي بيانات';
                        $message_type = 'warning';
                    }
                } catch (Exception $e) {
                    $message = 'حدث خطأ أثناء تحديث البيانات';
                    $message_type = 'error';
                }
            } else {
                $message = implode('<br>', $errors);
                $message_type = 'error';
            }
        }
        
        elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            $errors = [];
            
            if (empty($current_password)) {
                $errors[] = 'كلمة المرور الحالية مطلوبة';
            } elseif (!password_verify($current_password, $user['password'])) {
                $errors[] = 'كلمة المرور الحالية غير صحيحة';
            }
            
            if (empty($new_password)) {
                $errors[] = 'كلمة المرور الجديدة مطلوبة';
            } elseif (strlen($new_password) < PASSWORD_MIN_LENGTH) {
                $errors[] = 'كلمة المرور يجب أن تكون ' . PASSWORD_MIN_LENGTH . ' أحرف على الأقل';
            }
            
            if ($new_password !== $confirm_password) {
                $errors[] = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
            }
            
            if (empty($errors)) {
                try {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $updated = $database->update('users', ['password' => $hashed_password], 'id = ?', [$user_id]);
                    
                    if ($updated) {
                        $message = 'تم تغيير كلمة المرور بنجاح';
                        $message_type = 'success';
                    } else {
                        $message = 'لم يتم تغيير كلمة المرور';
                        $message_type = 'warning';
                    }
                } catch (Exception $e) {
                    $message = 'حدث خطأ أثناء تغيير كلمة المرور';
                    $message_type = 'error';
                }
            } else {
                $message = implode('<br>', $errors);
                $message_type = 'error';
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-md-4 sidebar">
            <div class="text-center text-white py-4">
                <div class="mb-3">
                    <?php if ($user['university_card_image']): ?>
                        <img src="../assets/uploads/<?php echo htmlspecialchars($user['university_card_image']); ?>" 
                             class="rounded-circle" width="80" height="80" style="object-fit: cover;">
                    <?php else: ?>
                        <i class="fas fa-user-circle fa-5x"></i>
                    <?php endif; ?>
                </div>
                <h5 class="mb-1"><?php echo htmlspecialchars($user['full_name']); ?></h5>
                <p class="mb-0 small"><?php echo htmlspecialchars($user['student_id']); ?></p>
            </div>
            
            <nav class="nav flex-column">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                </a>
                <a class="nav-link" href="courses.php">
                    <i class="fas fa-book me-2"></i>دوراتي
                </a>
                <a class="nav-link" href="browse-courses.php">
                    <i class="fas fa-search me-2"></i>تصفح الدورات
                </a>
                <a class="nav-link active" href="profile.php">
                    <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                </a>
                <a class="nav-link" href="notifications.php">
                    <i class="fas fa-bell me-2"></i>الإشعارات
                </a>
                <a class="nav-link" href="certificates.php">
                    <i class="fas fa-certificate me-2"></i>الشهادات
                </a>
                <a class="nav-link" href="payments.php">
                    <i class="fas fa-credit-card me-2"></i>المدفوعات
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="col-lg-9 col-md-8 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-edit me-2"></i>الملف الشخصي</h2>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $message_type === 'error' ? 'danger' : ($message_type === 'warning' ? 'warning' : 'success'); ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <!-- معلومات الحساب -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>معلومات الحساب</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_id" class="form-label">رقم الطالب</label>
                                        <input type="text" class="form-control" id="student_id" 
                                               value="<?php echo htmlspecialchars($user['student_id']); ?>" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="university_level" class="form-label">المرحلة الجامعية</label>
                                        <select class="form-select" id="university_level" name="university_level">
                                            <option value="">اختر المرحلة</option>
                                            <option value="السنة الأولى" <?php echo $user['university_level'] === 'السنة الأولى' ? 'selected' : ''; ?>>السنة الأولى</option>
                                            <option value="السنة الثانية" <?php echo $user['university_level'] === 'السنة الثانية' ? 'selected' : ''; ?>>السنة الثانية</option>
                                            <option value="السنة الثالثة" <?php echo $user['university_level'] === 'السنة الثالثة' ? 'selected' : ''; ?>>السنة الثالثة</option>
                                            <option value="السنة الرابعة" <?php echo $user['university_level'] === 'السنة الرابعة' ? 'selected' : ''; ?>>السنة الرابعة</option>
                                            <option value="السنة الخامسة" <?php echo $user['university_level'] === 'السنة الخامسة' ? 'selected' : ''; ?>>السنة الخامسة</option>
                                            <option value="دراسات عليا" <?php echo $user['university_level'] === 'دراسات عليا' ? 'selected' : ''; ?>>دراسات عليا</option>
                                            <option value="خريج" <?php echo $user['university_level'] === 'خريج' ? 'selected' : ''; ?>>خريج</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="specialization" class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="specialization" name="specialization" 
                                               value="<?php echo htmlspecialchars($user['specialization']); ?>">
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- تغيير كلمة المرور -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية *</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                                        <input type="password" class="form-control" id="new_password" name="new_password" 
                                               minlength="<?php echo PASSWORD_MIN_LENGTH; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الحساب</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>حالة الحساب:</strong>
                                <span class="badge status-<?php echo $user['account_status']; ?> ms-2">
                                    <?php 
                                    $status_text = [
                                        'pending' => 'قيد المراجعة',
                                        'approved' => 'مفعل',
                                        'rejected' => 'مرفوض'
                                    ];
                                    echo $status_text[$user['account_status']] ?? $user['account_status'];
                                    ?>
                                </span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>تاريخ التسجيل:</strong><br>
                                <small class="text-muted"><?php echo format_date($user['created_at'], 'Y/m/d H:i'); ?></small>
                            </div>
                            
                            <?php if ($user['last_login']): ?>
                            <div class="mb-3">
                                <strong>آخر دخول:</strong><br>
                                <small class="text-muted"><?php echo format_date($user['last_login'], 'Y/m/d H:i'); ?></small>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($user['university_card_image']): ?>
                            <div class="mb-3">
                                <strong>البطاقة الجامعية:</strong><br>
                                <img src="../assets/uploads/<?php echo htmlspecialchars($user['university_card_image']); ?>" 
                                     class="img-thumbnail mt-2" style="max-width: 200px;">
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 2px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    transform: translateX(5px);
}

.main-content {
    padding: 20px;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-approved {
    background-color: #28a745;
    color: #fff;
}

.status-rejected {
    background-color: #dc3545;
    color: #fff;
}
</style>

<script>
// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});
</script>

<?php include '../includes/footer.php'; ?>
