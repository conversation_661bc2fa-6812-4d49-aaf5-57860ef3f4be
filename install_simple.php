<?php
/**
 * معالج التثبيت المبسط
 * Simple Installation Handler
 */

session_start();

$step = $_POST['step'] ?? $_GET['step'] ?? 1;
$error = '';
$success = '';

// إعدادات قاعدة البيانات الافتراضية
$default_config = [
    'db_host' => 'localhost',
    'db_name' => 'elzam_training_system',
    'db_user' => 'root',
    'db_pass' => ''
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // إعداد قاعدة البيانات
        $db_host = $_POST['db_host'] ?? $default_config['db_host'];
        $db_name = $_POST['db_name'] ?? $default_config['db_name'];
        $db_user = $_POST['db_user'] ?? $default_config['db_user'];
        $db_pass = $_POST['db_pass'] ?? $default_config['db_pass'];
        
        try {
            // اختبار الاتصال بالخادم
            $pdo = new PDO("mysql:host={$db_host}", $db_user, $db_pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // الاتصال بقاعدة البيانات
            $pdo = new PDO("mysql:host={$db_host};dbname={$db_name};charset=utf8mb4", $db_user, $db_pass, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            // إنشاء الجداول
            $tables_sql = [
                // جدول المستخدمين
                "CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    student_id VARCHAR(20) UNIQUE NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    university_level VARCHAR(50) NOT NULL,
                    specialization VARCHAR(100) NOT NULL,
                    university_card_image VARCHAR(255),
                    account_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    rejection_reason TEXT NULL,
                    user_type ENUM('student', 'admin') DEFAULT 'student',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL,
                    is_active BOOLEAN DEFAULT TRUE
                )",
                
                // جدول الدورات
                "CREATE TABLE IF NOT EXISTS courses (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    course_name VARCHAR(150) NOT NULL,
                    course_description TEXT,
                    instructor_name VARCHAR(100) NOT NULL,
                    course_image VARCHAR(255),
                    duration_hours INT NOT NULL,
                    price DECIMAL(10,2) NOT NULL,
                    start_date DATE,
                    end_date DATE,
                    max_students INT DEFAULT 50,
                    current_enrolled INT DEFAULT 0,
                    course_link VARCHAR(500),
                    is_link_active BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )",
                
                // جدول التسجيلات
                "CREATE TABLE IF NOT EXISTS enrollments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    course_id INT NOT NULL,
                    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                    payment_id VARCHAR(100),
                    enrollment_status ENUM('enrolled', 'completed', 'dropped') DEFAULT 'enrolled',
                    completion_date TIMESTAMP NULL,
                    certificate_issued BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_enrollment (user_id, course_id)
                )",
                
                // جدول سجلات النشاط
                "CREATE TABLE IF NOT EXISTS activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT,
                    activity_type VARCHAR(50) NOT NULL,
                    activity_description TEXT,
                    page_accessed VARCHAR(255),
                    session_id VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
                )"
            ];
            
            // تنفيذ إنشاء الجداول
            foreach ($tables_sql as $sql) {
                $pdo->exec($sql);
            }
            
            // إدراج المدير الافتراضي
            $admin_exists = $pdo->query("SELECT COUNT(*) FROM users WHERE user_type = 'admin'")->fetchColumn();
            if ($admin_exists == 0) {
                $default_password = password_hash('admin123', PASSWORD_DEFAULT);
                $pdo->exec("INSERT INTO users (student_id, full_name, email, password, university_level, specialization, user_type, account_status) 
                           VALUES ('ADMIN001', 'مدير النظام', '<EMAIL>', '{$default_password}', 'خريج', 'إدارة نظم', 'admin', 'approved')");
            }
            
            // حفظ إعدادات قاعدة البيانات
            $_SESSION['db_config'] = [
                'host' => $db_host,
                'name' => $db_name,
                'user' => $db_user,
                'pass' => $db_pass
            ];
            
            $step = 2;
            $success = 'تم إعداد قاعدة البيانات بنجاح! تم إنشاء حساب مدير افتراضي: <EMAIL> / admin123';
            
        } catch (Exception $e) {
            $error = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
        }
        
    } elseif ($step == 2) {
        // إنهاء التثبيت
        $step = 3;
        $success = 'تم التثبيت بنجاح!';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام الزام - مبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .install-container {
            max-width: 600px;
            margin: 50px auto;
        }
        
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-card">
                <div class="install-header">
                    <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                    <h2>تثبيت نظام الزام</h2>
                    <p class="mb-0">معالج التثبيت المبسط</p>
                </div>
                
                <div class="p-4">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($step == 1): ?>
                        <h4 class="mb-4">إعداد قاعدة البيانات</h4>
                        
                        <form method="POST">
                            <input type="hidden" name="step" value="1">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo $default_config['db_host']; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo $default_config['db_name']; ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_user" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo $default_config['db_user']; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_pass" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo $default_config['db_pass']; ?>">
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-database me-2"></i>إعداد قاعدة البيانات
                            </button>
                        </form>
                        
                    <?php elseif ($step == 2): ?>
                        <div class="text-center">
                            <h4 class="mb-4">إكمال التثبيت</h4>
                            <p class="text-muted mb-4">تم إعداد قاعدة البيانات بنجاح. اضغط على "إنهاء التثبيت" للمتابعة.</p>
                            
                            <form method="POST" class="d-grid gap-2">
                                <input type="hidden" name="step" value="2">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-2"></i>إنهاء التثبيت
                                </button>
                            </form>
                        </div>
                        
                    <?php elseif ($step == 3): ?>
                        <div class="text-center">
                            <i class="fas fa-check-circle fa-5x text-success mb-4"></i>
                            <h3 class="text-success mb-4">تم التثبيت بنجاح!</h3>
                            
                            <div class="alert alert-info text-start">
                                <h6><i class="fas fa-info-circle me-2"></i>بيانات تسجيل الدخول:</h6>
                                <strong>البريد الإلكتروني:</strong> <EMAIL><br>
                                <strong>كلمة المرور:</strong> admin123
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="index.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                </a>
                                <a href="auth/login.php" class="btn btn-success">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
