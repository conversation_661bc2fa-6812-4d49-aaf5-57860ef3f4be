/* 
 * ملف التنسيق الرئيسي لموقع الزام التعليمي
 * Main CSS File for Elzam Educational Platform
 */

/* الخطوط والإعدادات العامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.navbar-brand {
    font-weight: 700;
    color: white !important;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: white !important;
    font-weight: 500;
    margin: 0 10px;
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 8px 15px !important;
}

.navbar-nav .nav-link:hover {
    color: #ffd700 !important;
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,.15);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 25px;
    padding: 10px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.btn-success:hover {
    background: linear-gradient(135deg, #4a9628 0%, #95d3b8 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e6395f 0%, #e64327 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
    color: #333;
}

.btn-info {
    background: linear-gradient(135deg, #00d2ff 0%, #3a7bd5 100%);
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 15px;
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0,0,0,.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* حقول الإدخال */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* الجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
    border-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* الشارات */
.badge {
    font-size: 0.8em;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
}

.status-pending {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #2d3436;
}

.status-approved {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
}

.status-rejected {
    background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    color: white;
}

.status-active {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    color: white;
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,.1);
}

.sidebar .nav-link {
    color: white;
    padding: 15px 25px;
    margin: 5px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: rgba(255,255,255,0.2);
    color: #ffd700;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    padding: 30px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* بطاقات الدورات */
.course-card {
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,.15);
}

.course-image {
    height: 200px;
    object-fit: cover;
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.course-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
}

.course-instructor {
    color: #6c757d;
    font-weight: 500;
}

/* شارة الإشعارات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,.08);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,.15);
}

.stats-card.primary {
    border-left-color: #667eea;
}

.stats-card.success {
    border-left-color: #28a745;
}

.stats-card.warning {
    border-left-color: #ffc107;
}

.stats-card.danger {
    border-left-color: #dc3545;
}

.stats-card .icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stats-card .label {
    color: #6c757d;
    font-weight: 500;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
        padding: 15px 0;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .navbar-nav .nav-link {
        margin: 5px 0;
    }
    
    .btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
    
    .course-card {
        margin-bottom: 20px;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: 15px;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .navbar,
    footer,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات إضافية */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0,0,0,.1) !important;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%) 1;
}
